﻿@rendermode InteractiveAuto

@implements IDisposable

@inject NavigationManager NavigationManager

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon" />
    <label for="navmenu-toggle" class="navmenu-icon"><FluentIcon Value="@(new Icons.Regular.Size20.Navigation())" Color="Color.Fill" /></label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Width="250" Collapsible="true" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">
            <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Icons.Regular.Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>

            <!-- HR System - Fluent UI -->
            <FluentNavGroup Text="HR System (Fluent UI)" Icon="@(new Icons.Regular.Size20.People())" IconColor="Color.Accent">
                <FluentNavLink Href="employees" Icon="@(new Icons.Regular.Size20.People())" IconColor="Color.Accent">Employee List</FluentNavLink>
            </FluentNavGroup>

            <!-- HR System - Tailwind CSS -->
            <FluentNavGroup Text="HR System (Tailwind)" Icon="@(new Icons.Regular.Size20.PaintBrush())" IconColor="Color.Info">
                <FluentNavLink Href="employees-tailwind" Icon="@(new Icons.Regular.Size20.People())" IconColor="Color.Info">Employee List</FluentNavLink>
            </FluentNavGroup>

            <FluentNavLink Href="counter" Icon="@(new Icons.Regular.Size20.NumberSymbolSquare())" IconColor="Color.Accent">Counter</FluentNavLink>
            <FluentNavLink Href="weather" Icon="@(new Icons.Regular.Size20.WeatherPartlyCloudyDay())" IconColor="Color.Accent">Weather</FluentNavLink>
            <FluentNavLink Href="auth" Icon="@(new Icons.Regular.Size20.LockClosedKey())" IconColor="Color.Accent">Auth Required</FluentNavLink>
            <AuthorizeView>
                <Authorized>
                    <FluentNavLink Href="Account/Manage">@context.User.Identity?.Name</FluentNavLink>
                    <form action="Account/Logout" method="post">
                        <AntiforgeryToken />
                        <input type="hidden" name="ReturnUrl" value="@currentUrl" />
                        <FluentButton Type="ButtonType.Submit" Style="width: 100%;">Logout</FluentButton>
                    </form>
                </Authorized>
                <NotAuthorized>
                    <FluentNavLink Href="Account/Register">Register</FluentNavLink>
                    <FluentNavLink Href="Account/Login">Login</FluentNavLink>
                </NotAuthorized>
            </AuthorizeView>
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;

    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}
