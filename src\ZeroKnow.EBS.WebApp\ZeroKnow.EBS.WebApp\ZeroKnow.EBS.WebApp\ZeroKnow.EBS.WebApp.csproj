<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-ZeroKnow.EBS.WebApp-c0c58744-95eb-463d-a09a-10b3cf58a018</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Services\EmployeeService.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ZeroKnow.EBS.WebApp.Client\ZeroKnow.EBS.WebApp.Client.csproj" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.12.1" />
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.12.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Services\" />
  </ItemGroup>
</Project>
