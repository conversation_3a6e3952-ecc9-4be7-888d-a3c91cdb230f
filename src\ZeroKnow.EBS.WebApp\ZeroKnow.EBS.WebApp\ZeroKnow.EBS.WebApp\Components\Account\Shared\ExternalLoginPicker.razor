﻿@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using ZeroKnow.EBS.WebApp.Data

@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityRedirectManager RedirectManager

@if (externalLogins.Length == 0)
{
    <div>
        <p>
            There are no external authentication services configured. See this <a href="https://go.microsoft.com/fwlink/?LinkID=532715">article
            about setting up this ASP.NET application to support logging in via external services</a>.
        </p>
    </div>
}
else
{
    <form class="form-horizontal" action="Account/PerformExternalLogin" method="post">
        <div>
            <AntiforgeryToken />
            <input type="hidden" name="ReturnUrl" value="@ReturnUrl" />
            <p>
                @foreach (var provider in externalLogins)
                {
                    <FluentButton Type="ButtonType.Submit" Appearance="Appearance.Accent" Name="provider" Value="@provider.Name" Title="@($"Log in using your {provider.DisplayName} account")">@provider.DisplayName</FluentButton>
                    <text>&nbsp;</text>
                }
            </p>
        </div>
    </form>
}

@code {
    private AuthenticationScheme[] externalLogins = [];

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        externalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).ToArray();
    }
}
