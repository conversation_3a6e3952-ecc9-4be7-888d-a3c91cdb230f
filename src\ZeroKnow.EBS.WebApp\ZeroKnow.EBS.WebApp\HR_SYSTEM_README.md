# Human Resources System

A comprehensive HR management system built with Blazor and Fluent UI components.

## Features

### Employee Listing Page (`/employees`)
- **Card-based Layout**: Clean, modern card design displaying employee information
- **Search Functionality**: Real-time search across name, position, department, and email
- **Advanced Filtering**: 
  - Filter by department
  - Filter by employment status (Active, Inactive, On Leave, Terminated)
  - Sort by name, department, position, or hire date
- **Pagination**: Efficient pagination for large employee lists (12 employees per page)
- **Responsive Design**: Adapts to different screen sizes
- **Employee Photos**: Avatar support with fallback icons

### Employee Profile Detail View (`/employees/{id}`)
- **Comprehensive Profile Header**: Large photo, contact information, and quick actions
- **Tabbed Interface** with 5 main sections:

#### 1. Personal Information Tab
- Full name, date of birth
- Contact details (email, phone)
- Address information
- Emergency contact details

#### 2. Employment Details Tab
- Employee ID, position, department
- Manager information
- Hire date and years of service
- Employment status with color-coded badges

#### 3. Compensation Tab
- Annual salary information
- Pay frequency details
- Benefits package information
- Last salary review date

#### 4. Performance Tab
- Last review score with color-coded performance indicators
- Review dates and scheduling
- Performance notes and achievements

#### 5. Documents Tab
- Placeholder for future document management
- Employment contracts
- Certifications and training records

## Technical Implementation

### Architecture
- **Blazor Interactive Auto**: Supports both server-side and WebAssembly rendering
- **Fluent UI Components**: Modern, accessible UI components
- **Service-based Architecture**: Clean separation of concerns
- **Responsive CSS**: Mobile-first design approach

### Key Components
- `Employee.cs`: Core data model with comprehensive employee information
- `EmployeeService.cs`: Service layer with sample data generation
- `Employees.razor`: Main listing page with search, filter, and pagination
- `EmployeeDetails.razor`: Detailed profile view with tabbed interface

### Sample Data
- 50 sample employees across 9 departments
- Realistic data including names, positions, contact information
- Random but consistent data generation for testing

### UI Features
- **Status Badges**: Color-coded employment status indicators
- **Performance Indicators**: Visual performance score representation
- **Loading States**: Proper loading indicators for async operations
- **Error Handling**: User-friendly error messages
- **Accessibility**: ARIA labels and keyboard navigation support

## Navigation
- Accessible via the main navigation menu
- Breadcrumb navigation in detail views
- Direct linking to specific employee profiles

## Future Enhancements
- Document upload and management
- Employee photo upload
- Advanced reporting and analytics
- Employee onboarding workflows
- Performance review management
- Integration with payroll systems

## Usage
1. Navigate to `/employees` to view the employee listing
2. Use search and filters to find specific employees
3. Click on any employee card or the arrow button to view details
4. Navigate between tabs to view different aspects of employee information
5. Use the back button to return to the employee listing

## Responsive Design
- Desktop: Multi-column card layout with full feature set
- Tablet: Responsive grid that adapts to screen size
- Mobile: Single-column layout with optimized touch interactions

The system provides a solid foundation for HR management with room for future expansion and customization based on specific organizational needs.
