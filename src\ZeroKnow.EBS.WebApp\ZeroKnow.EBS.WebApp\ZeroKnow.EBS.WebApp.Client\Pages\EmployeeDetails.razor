@page "/employees/{EmployeeId:int}"
@using ZeroKnow.EBS.WebApp.Client.Models
@using ZeroKnow.EBS.WebApp.Client.Services
@inject IEmployeeService EmployeeService
@inject NavigationManager Navigation
@rendermode InteractiveAuto

<PageTitle>@(employee?.FullName ?? "Employee Details")</PageTitle>

@if (isLoading)
{
    <FluentStack Orientation="Orientation.Horizontal" Style="justify-content: center; padding: 2rem;">
        <FluentProgressRing />
        <span>Loading employee details...</span>
    </FluentStack>
}
else if (employee == null)
{
    <FluentMessageBar Intent="MessageIntent.Error">
        Employee not found.
    </FluentMessageBar>
    <FluentButton Appearance="Appearance.Accent" @onclick="@(() => Navigation.NavigateTo("/employees"))">
        Back to Employees
    </FluentButton>
}
else
{
    <FluentStack Orientation="Orientation.Vertical" Style="gap: 1.5rem;">
        <!-- Header with <PERSON> Button -->
        <FluentStack Orientation="Orientation.Horizontal" Style="align-items: center; gap: 1rem;">
            <FluentButton Appearance="Appearance.Stealth"
                        @onclick="@(() => Navigation.NavigateTo("/employees"))"
                        Title="Back to Employees">
                <FluentIcon Value="@(new Icons.Regular.Size20.ArrowLeft())" />
            </FluentButton>
            <h1 style="margin: 0;">Employee Profile</h1>
        </FluentStack>

        <!-- Employee Header Card -->
        <FluentCard Style="padding: 1.5rem;">
            <FluentStack Orientation="Orientation.Horizontal" Style="gap: 1.5rem; align-items: center;">
                <!-- Employee Photo -->
                <div class="employee-photo-large">
                    @if (!string.IsNullOrEmpty(employee.PhotoUrl))
                    {
                        <img src="@employee.PhotoUrl" alt="@employee.FullName" />
                    }
                    else
                    {
                        <FluentIcon Value="@(new Icons.Regular.Size48.Person())" />
                    }
                </div>
                
                <!-- Employee Basic Info -->
                <FluentStack Orientation="Orientation.Vertical" Style="flex: 1; gap: 0.5rem;">
                    <FluentStack Orientation="Orientation.Horizontal" Style="justify-content: space-between; align-items: center;">
                        <h2 style="margin: 0; font-size: 1.5rem;">@employee.FullName</h2>
                        <FluentBadge Appearance="@GetStatusAppearance(employee.Status)" Style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                            @employee.Status
                        </FluentBadge>
                    </FluentStack>
                    
                    <FluentStack Orientation="Orientation.Horizontal" Style="gap: 1rem; flex-wrap: wrap;">
                        <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                            <FluentIcon Value="@(new Icons.Regular.Size16.Briefcase())" />
                            <span style="font-weight: 500;">@employee.Position</span>
                        </FluentStack>
                        
                        <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                            <FluentIcon Value="@(new Icons.Regular.Size16.Building())" />
                            <span>@employee.Department</span>
                        </FluentStack>
                        
                        <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                            <FluentIcon Value="@(new Icons.Regular.Size16.Calendar())" />
                            <span>Hired: @employee.HireDate.ToString("MMM dd, yyyy")</span>
                        </FluentStack>
                    </FluentStack>
                    
                    <FluentStack Orientation="Orientation.Horizontal" Style="gap: 1rem; flex-wrap: wrap;">
                        <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                            <FluentIcon Value="@(new Icons.Regular.Size16.Mail())" />
                            <a href="mailto:@employee.Email" style="text-decoration: none; color: var(--accent-foreground-rest);">@employee.Email</a>
                        </FluentStack>
                        
                        @if (!string.IsNullOrEmpty(employee.PhoneNumber))
                        {
                            <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                                <FluentIcon Value="@(new Icons.Regular.Size16.Phone())" />
                                <a href="tel:@employee.PhoneNumber" style="text-decoration: none; color: var(--accent-foreground-rest);">@employee.PhoneNumber</a>
                            </FluentStack>
                        }
                    </FluentStack>
                </FluentStack>
                
                <!-- Action Buttons -->
                <FluentStack Orientation="Orientation.Vertical" Style="gap: 0.5rem;">
                    <FluentButton Appearance="Appearance.Accent">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Edit())" />
                        Edit Profile
                    </FluentButton>
                    <FluentButton Appearance="Appearance.Outline">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Print())" />
                        Print
                    </FluentButton>
                </FluentStack>
            </FluentStack>
        </FluentCard>

        <!-- Tabbed Content -->
        <FluentTabs @bind-ActiveTabId="activeTabId" Style="width: 100%;">
            <!-- Personal Information Tab -->
            <FluentTab Id="personal" Text="Personal Information">
                <FluentCard Style="padding: 1.5rem; margin-top: 1rem;">
                    <h3 style="margin-top: 0;">
                        <FluentIcon Value="@(new Icons.Regular.Size20.Person())" Style="margin-right: 0.5rem;" />
                        Personal Information
                    </h3>
                    
                    <FluentGrid>
                        <FluentGridItem xs="12" sm="6">
                            <FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem;">
                                <div class="info-field">
                                    <label class="info-label">Full Name</label>
                                    <span class="info-value">@employee.FullName</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Date of Birth</label>
                                    <span class="info-value">@(employee.DateOfBirth?.ToString("MMM dd, yyyy") ?? "Not specified")</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Email Address</label>
                                    <span class="info-value">@employee.Email</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Phone Number</label>
                                    <span class="info-value">@(employee.PhoneNumber ?? "Not specified")</span>
                                </div>
                            </FluentStack>
                        </FluentGridItem>
                        
                        <FluentGridItem xs="12" sm="6">
                            <FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem;">
                                <div class="info-field">
                                    <label class="info-label">Address</label>
                                    <span class="info-value">@(employee.Address ?? "Not specified")</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Emergency Contact</label>
                                    <span class="info-value">@(employee.EmergencyContactName ?? "Not specified")</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Emergency Phone</label>
                                    <span class="info-value">@(employee.EmergencyContactPhone ?? "Not specified")</span>
                                </div>
                            </FluentStack>
                        </FluentGridItem>
                    </FluentGrid>
                </FluentCard>
            </FluentTab>

            <!-- Employment Details Tab -->
            <FluentTab Id="employment" Text="Employment Details">
                <FluentCard Style="padding: 1.5rem; margin-top: 1rem;">
                    <h3 style="margin-top: 0;">
                        <FluentIcon Value="@(new Icons.Regular.Size20.Briefcase())" Style="margin-right: 0.5rem;" />
                        Employment Details
                    </h3>
                    
                    <FluentGrid>
                        <FluentGridItem xs="12" sm="6">
                            <FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem;">
                                <div class="info-field">
                                    <label class="info-label">Employee ID</label>
                                    <span class="info-value">@employee.Id.ToString("D6")</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Position</label>
                                    <span class="info-value">@employee.Position</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Department</label>
                                    <span class="info-value">@employee.Department</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Manager</label>
                                    <span class="info-value">@(employee.ManagerName ?? "Not assigned")</span>
                                </div>
                            </FluentStack>
                        </FluentGridItem>
                        
                        <FluentGridItem xs="12" sm="6">
                            <FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem;">
                                <div class="info-field">
                                    <label class="info-label">Hire Date</label>
                                    <span class="info-value">@employee.HireDate.ToString("MMM dd, yyyy")</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Employment Status</label>
                                    <FluentBadge Appearance="@GetStatusAppearance(employee.Status)">@employee.Status</FluentBadge>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Years of Service</label>
                                    <span class="info-value">@GetYearsOfService(employee.HireDate) years</span>
                                </div>
                            </FluentStack>
                        </FluentGridItem>
                    </FluentGrid>
                </FluentCard>
            </FluentTab>

            <!-- Compensation Tab -->
            <FluentTab Id="compensation" Text="Compensation">
                <FluentCard Style="padding: 1.5rem; margin-top: 1rem;">
                    <h3 style="margin-top: 0;">
                        <FluentIcon Value="@(new Icons.Regular.Size20.Money())" Style="margin-right: 0.5rem;" />
                        Compensation & Benefits
                    </h3>
                    
                    <FluentGrid>
                        <FluentGridItem xs="12" sm="6">
                            <FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem;">
                                <div class="info-field">
                                    <label class="info-label">Annual Salary</label>
                                    <span class="info-value">@(employee.Salary?.ToString("C") ?? "Not specified")</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Pay Frequency</label>
                                    <span class="info-value">Bi-weekly</span>
                                </div>
                            </FluentStack>
                        </FluentGridItem>
                        
                        <FluentGridItem xs="12" sm="6">
                            <FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem;">
                                <div class="info-field">
                                    <label class="info-label">Benefits Package</label>
                                    <span class="info-value">Standard Package</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Last Salary Review</label>
                                    <span class="info-value">@(employee.LastReviewDate?.ToString("MMM dd, yyyy") ?? "Not available")</span>
                                </div>
                            </FluentStack>
                        </FluentGridItem>
                    </FluentGrid>
                </FluentCard>
            </FluentTab>

            <!-- Performance Tab -->
            <FluentTab Id="performance" Text="Performance">
                <FluentCard Style="padding: 1.5rem; margin-top: 1rem;">
                    <h3 style="margin-top: 0;">
                        <FluentIcon Value="@(new Icons.Regular.Size20.Trophy())" Style="margin-right: 0.5rem;" />
                        Performance & Reviews
                    </h3>
                    
                    <FluentGrid>
                        <FluentGridItem xs="12" sm="6">
                            <FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem;">
                                <div class="info-field">
                                    <label class="info-label">Last Review Score</label>
                                    <span class="info-value">
                                        @if (employee.LastReviewScore.HasValue)
                                        {
                                            <FluentBadge Appearance="@GetPerformanceAppearance(employee.LastReviewScore.Value)">
                                                @employee.LastReviewScore.Value.ToString("F1")/5.0
                                            </FluentBadge>
                                        }
                                        else
                                        {
                                            <span>Not available</span>
                                        }
                                    </span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Last Review Date</label>
                                    <span class="info-value">@(employee.LastReviewDate?.ToString("MMM dd, yyyy") ?? "Not available")</span>
                                </div>
                            </FluentStack>
                        </FluentGridItem>
                        
                        <FluentGridItem xs="12" sm="6">
                            <FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem;">
                                <div class="info-field">
                                    <label class="info-label">Next Review Due</label>
                                    <span class="info-value">@GetNextReviewDate(employee.LastReviewDate)</span>
                                </div>
                                
                                <div class="info-field">
                                    <label class="info-label">Performance Notes</label>
                                    <span class="info-value">@(employee.Notes ?? "No notes available")</span>
                                </div>
                            </FluentStack>
                        </FluentGridItem>
                    </FluentGrid>
                </FluentCard>
            </FluentTab>

            <!-- Documents Tab -->
            <FluentTab Id="documents" Text="Documents">
                <FluentCard Style="padding: 1.5rem; margin-top: 1rem;">
                    <h3 style="margin-top: 0;">
                        <FluentIcon Value="@(new Icons.Regular.Size20.Document())" Style="margin-right: 0.5rem;" />
                        Documents & Records
                    </h3>
                    
                    <FluentMessageBar Intent="MessageIntent.Info">
                        Document management functionality will be implemented in a future update.
                    </FluentMessageBar>
                    
                    <FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem; margin-top: 1rem;">
                        <div class="document-placeholder">
                            <FluentIcon Value="@(new Icons.Regular.Size24.Document())" />
                            <span>Employment Contract</span>
                            <FluentButton Appearance="Appearance.Stealth" Disabled="true">View</FluentButton>
                        </div>
                        
                        <div class="document-placeholder">
                            <FluentIcon Value="@(new Icons.Regular.Size24.Certificate())" />
                            <span>Certifications</span>
                            <FluentButton Appearance="Appearance.Stealth" Disabled="true">View</FluentButton>
                        </div>
                        
                        <div class="document-placeholder">
                            <FluentIcon Value="@(new Icons.Regular.Size24.BookOpen())" />
                            <span>Training Records</span>
                            <FluentButton Appearance="Appearance.Stealth" Disabled="true">View</FluentButton>
                        </div>
                    </FluentStack>
                </FluentCard>
            </FluentTab>
        </FluentTabs>
    </FluentStack>
}

<style>
    .employee-photo-large {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--neutral-layer-2);
        flex-shrink: 0;
        border: 3px solid var(--neutral-stroke-divider);
    }

    .employee-photo-large img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .info-field {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .info-label {
        font-weight: 600;
        color: var(--neutral-foreground-hint);
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-value {
        font-size: 1rem;
        color: var(--neutral-foreground-rest);
        word-break: break-word;
    }

    .document-placeholder {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border: 1px dashed var(--neutral-stroke-divider);
        border-radius: var(--control-corner-radius);
        background: var(--neutral-layer-1);
    }

    .document-placeholder span {
        flex: 1;
        font-weight: 500;
    }

    @@media (max-width: 768px) {
        .employee-photo-large {
            width: 80px;
            height: 80px;
        }
    }
</style>

@code {
    [Parameter] public int EmployeeId { get; set; }

    private Employee? employee;
    private bool isLoading = true;
    private string activeTabId = "personal";

    protected override async Task OnInitializedAsync()
    {
        await LoadEmployee();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (employee?.Id != EmployeeId)
        {
            await LoadEmployee();
        }
    }

    private async Task LoadEmployee()
    {
        isLoading = true;
        StateHasChanged();

        employee = await EmployeeService.GetEmployeeByIdAsync(EmployeeId);

        isLoading = false;
        StateHasChanged();
    }

    private Appearance GetStatusAppearance(EmploymentStatus status)
    {
        return status switch
        {
            EmploymentStatus.Active => Appearance.Accent,
            EmploymentStatus.OnLeave => Appearance.Neutral,
            EmploymentStatus.Inactive => Appearance.Lightweight,
            EmploymentStatus.Terminated => Appearance.Stealth,
            _ => Appearance.Neutral
        };
    }

    private Appearance GetPerformanceAppearance(double score)
    {
        return score switch
        {
            >= 4.5 => Appearance.Accent,
            >= 3.5 => Appearance.Neutral,
            >= 2.5 => Appearance.Lightweight,
            _ => Appearance.Stealth
        };
    }

    private double GetYearsOfService(DateTime hireDate)
    {
        var timeSpan = DateTime.Now - hireDate;
        return Math.Round(timeSpan.TotalDays / 365.25, 1);
    }

    private string GetNextReviewDate(DateTime? lastReviewDate)
    {
        if (!lastReviewDate.HasValue)
            return "Not scheduled";

        var nextReview = lastReviewDate.Value.AddYears(1);
        if (nextReview < DateTime.Now)
            return "Overdue";

        return nextReview.ToString("MMM dd, yyyy");
    }
}
