{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["dO5R91H8BF0ERQNaCrTmplLf+/5sGLMprvfecE63P9o=", "InoZllr5mETBb6nMPSKg6QIAyESdpH0b6W5JuG/VHUw=", "Yd8qrl1tHViAPTYeIPi+hkQKk1hr7k87r2AfQ8McOfY=", "+FTftDRHryEP8DkjyCZ8M88d4eto1yJDk9yQe2s9ntE=", "fWwvaNTsCVKlnS61k9dH9CKRkYwWs/ZfZ9EH1Sf975A=", "B7T8OVrjIjd6AdhFtxGzFRaVDM1r1LR1zhG5G8OzAPI=", "NGWYBFrRuR/n/XgUSC2Rpiw6MCjg/Fzv1TdfQ9Fs93Y=", "x+5Zjlzu2ShYLdTKNjTY+768THrsCNua32lHc/2N3Bw=", "jLjiHtzFKkWmTkPCgu6P8lc1mC7KDFLyHaX4R7fpp5M=", "AYDOuSWjQRHVQy1/waiknXi+kPLCK4NHQp/KLsg9Uww=", "161leAbtUpkK1yEkzNx7lT35U7GZMsdXZIsshJOu8js=", "leoajpcmje2PrE36IfzaclOgihpBoRPVrFBthx1Gjms=", "aVdpbL7m5bCsE0TD9v8W1IrQ1KHgoL7Xu+PNXXs8Ef0=", "EHH4FmFPnpu/1ATkNV6Oz9wx743fFHGK8GNz2Zu/Wto=", "DhI5mTJsgUXlDRu4azTn8rnGwDrD5wHsWd0LQagQcsE=", "YvM3tgF7SP5F1d+r7EemqVDNFij+nwjyIYKHvJh1GrI=", "5zKcM5f1SOM2xEI8Yj0k5M81ZdhwCjor9upWhSj4TO4=", "HkH6YuAawMmMuG2RHyLh1Yn63CQrD6khs9CGUCUeuIc=", "Qekm+D75Cy8Qn+apdjYiHIAdOnTjJTRuQckzNxKnLMU=", "dvVkKnP9K4kPhzkSh0r71/GqET1xkKt6XweqbyTU2SY=", "/dhDTHicwK7/psfgevxyFVrlHQEpP5zf8Fzza+d3HHU=", "vfBNsDLM9Bx13LYcko8sb/m/gPNfPjwPlr5NE3TCtGM=", "svDe+iWl6Iicbkptm9huac/tOEI1O4Rtx+dm4a4aCD8=", "kcWyJ8Ko2/EPwcFChpHkHwn+qOTVwxiBuxEIahk2H8U=", "spdnQ+rP9bJYf3p8sCC8QDlTceTC1T43y+aK6uwHVvI=", "gHBLKYDjxD3tO/mtPBmDdNrj25X93YDEpe9Zj8dkSQM=", "hnHl9ZCPNBfFij94BXbFVrWQYXN3a1difIry3qfl5KA=", "GGFhDK2HMazmlxPbkU6qsvLsrmSju7mlpjn8WJBTQ/g=", "vnCtxfX+6h/4N6gNPWp9+lW0DFhMav4ghNBJHThB7EA=", "bvdg5snHksL58VokBp5ly6J+lfyHOPOujfnTT1IV36E=", "FWMeuyYQ/4bLD3kuY8U0xaX6f/KK1/Oui/RKszegi6A=", "VVBqFQkV8DQP+y/dYj26SSqT3UW5xHkM8FBFfNpMzxw=", "VKl9RcvbEK+DhFQ5J/a0dq3tRQFyHtNZmLQcd1RQBaM=", "Y3HNaWzWIWk6v+BQppJwh76/P5SyR+Fu76DnJMBQ6aA=", "Ylyks9pe6Sp97vuppLD4FTEx5uOdVwGhTgE+arh8Ha8=", "iKSur2gvQZEP1k6sEdiV2Sz2mJy0UoPnBMXQpA0IpKA=", "+ddsgJka+n4G8aCjjlWUgZ54eA53V4e9qziZcNK1CrI=", "UBo9rNQdy8DaEaz/iISIBeMnLgFwicRxR0sXcUm94DM=", "dHD3iB26G75JgN0xfOptKnzPC6jVTXDiO9F0e+2Rk+A=", "VfGqZ2TfwbYKTDNlanxapJ5ad9vayHEIQGVSwunMqr0=", "Wt9onCva6Xrk37ZxmyZpp+6rtJwmxX724H47t+3di0c=", "jMxeDrNLq7r62rRCRLzD7Jw2sh0t+mgRui58o0ibV0g=", "7tjzpVgYpl/yTq3j705QZyK8g2XSyhwVDh+6pK+CGWw=", "8PlTz5zd7+hTzyNuEaoiZWAj5pjPVjDRXHY7f7C9xgM=", "GOnlvQevfteFxWHTwC4myJ9x3LVPns3l76ABaVGJoOA=", "491+78BIC6dQ2qVH/eCipi5nDgJobugiCF56SVSDX/A=", "jmYGalk0W9xe+yEZlydD4EAb0/p1poxvgxXzNiotoGY=", "gMc7ysLTq/M/RvrL/kLVWjJqZxEngT7cWDeLaUpMo/Q=", "IlMcCmnW9JH0JXZSBibLMeJ6EcKKp3GbEXpmhPztk/E=", "l7srGEAauqGTvx+/ucxqDbehCv6dP/Atn3aX2vGjfZ8=", "zbGHIfs93Rmrp+4pobRY/elRZLMNzqaJQLUAnXMz8PE=", "4ePBbQSxxaFuFSm8iOCxn5KzAIPUd/H4I7ylIlMrIug=", "LM03bVhZHyi7C01nCrfkpLHZk/tEWS3Ud+BUcuN/UIQ=", "jRwKN0xTHRMlv82GQEgvkaZ9959sVRjFfaye/y05XVo=", "I6GZmVXLuDZzLjKAiwtKOByMzqHibM0PMNVQxJXmjpo=", "FxH5liNMNznm8/JXJaSzjyLw+StB1/g3aVtIgFCj9Jg=", "EZbX4w2ET7wl1GhtTkRajGrAibifBWEYvJ+EZfmpzlM=", "KX2AstDXnivjn7jZWkZzyrjJgzqjAnsI5CcMqlXHEqs=", "ZLBiPrCdyEr2IzUi4Ari++ap5dKinvoJV4lH4E/4glg=", "NqimYDgJcClr8L1uKZjDYGUlml5ikuqeV5wqgfxbMD4=", "WS8zD/Eg/KKs2tWjGxgaN++9ZEND31p3jn+qRtmQPCE=", "hiaz8wRnuJ8jI5yWlLj0oBf5bQBTLc7KtGuoI8Dl6U8=", "OCsA0hswdTGvJC8FSlqvYtlCY+KMVuMPLBlxTJuNjJ0=", "Wu2c5Xd0Q6zaJx8ziM/wZd0ulbRmj+Xkp8bMk/AE5ug=", "kAjfXdoX5DSqS1r8BZYV1pEA05lTEw8FAdT9mYFwW/o=", "6JYaCR8JFcBNEGZpvI3vE5GGUEE4ybfXYw73HLD3g6o=", "6GqRZakMB/1WZMuJ4ywuBs/zaHAjABRIIICqvbAvNxQ=", "22st5553KKy67fqXM6SUW11UTchg6smBBsWvHHvea64=", "xKGiZJU1up8goPdpqZvYD/e+2ItBQkDAbAbbh0Yphwo=", "uuo1MEJoxi8Ye/Ew0o5w+VpRC8ruhyQKuqAmogpZYVs=", "Azv9IBRucAqzUAALhtWIF3033LjY0Er7seChy/EzuZc=", "A1rKofGzYiiTTaCWhRqwEaLI+dOVyJPJsP+hBN9Z7zk=", "oMd10TKwRRSEtyDgQMjaoeghmuaYLliSCyAxi4wslOw=", "a68C0pd+EFgHQzeM1BLF8f8e5x3L6Eolv2M65C46UCM=", "rLtr097Sz6mvRDZZEkx4hM5T+Pej5Vak3Pfvtk28rXA=", "AdxyYcOr6Zw65aczraDy3TfYNDvJvDu14bpFwh6Enl8=", "qCNTNywrOoJlNoRUWip/uxDQQaPnQUelLmPZe+Wl708=", "QrY5mgcCtqcyL/7tkb4vo/fuczO+cM7KFF9LJP/cVbI=", "2CljHW25ekqKGIa2jcdTlbmkm9QJjrzpYE6qxs6t7bo=", "oYQsrF3ZHF6kCyEtX1KmMEXM/80pahA2unbsnR95HC0=", "BjyaQdtJhC2JCNCaIZbCcAmmkVizf8kxt6ukJ4xvry8=", "GsJliBJnCheATZunJxqphoym6GFbWjRyVr0Ie4Ic+h8=", "b0TrnrF+ZO7xtpV48p6pd8s6ddIJp1cLrhdvNVKs/W0=", "Rm3xcZskon+fvnIlBqiorPxlr5+jwXvFdSNO1hbmZwE=", "hUEr0jWMqJffAZWuGF2yveKxY2BsejPZb2TdiBvJwy0=", "aoisyiY4s4t3VH9zmLjQGzWDu+Ded1Q0MqUvGH+IaU4=", "f/hRUF8o7elos4kCWhdLj63jOmNhenbk1+Gx8O1ZZa8=", "YbOCSu/RYYOoB1VWcaZnbNehTrkKcuydct9Zo2wM8gU=", "kJLvVXBifDgwq/Q1KRLYM4+47+pWG8CYYL2fxgCgm8Y=", "OLXJgb5nGZHlnEpeaN9wwg4G4Use66eWbx40ZK5EkbU=", "1DVgNnlqz43sCtux7dU6v7yjzkwNoiGgC/1L3R2JBAQ=", "cqxv3+M82E01GhAqWCEQZ6Qj6QIa1cVrF4kOT3wlImk=", "Jat9bH2UGTe4QpjH8BfilNhRdYpwmi233+m4g5HIGFE=", "EqqpiZD9OJE98Dj+U3dg7ycZmYaU1XH4o3eO5wYJVGk=", "LrnLY+Bn74wpOKeUmkKdmJg/kvZd7+sPLZRAsepThI8=", "3xafzQuR4dx0jMqlvOpKHCC2roQ8Z1dN9wwowMz35P4=", "Lw9QonQ7RHWQOXaH2h+4hD9QIxH50RP6mp7e07/gQVY=", "mV/t3dqAFwq2D8Krhy/UnMb9pnowYvSftwGBvu+YL0k=", "PRytrM/QEASR82Glh8N3yTzecHHmJaug+fZwxg2Dzp0=", "q4+IvKmvBp/s9Ce7mk7iDBAx1/+d/aDfTW2B828muqg=", "ylSu1jzYJyBekGrZTvkAt7QEgtQ/7wzjLHpE68Vi04Y=", "vsU17Uqg47X7Sc0HPv3Lt9SyOBxROS+Rl3g7y62pwzI=", "hTiOlyoa8MVz8dUPM/atRdaQAab0RJpP4Awy4tX/WLU=", "Lcb3SBH5Mrt6fgOtYmkUZOdZFiDSFYY0g28xrZwWW3Y=", "57vOeCTXzS7E0d9dhOykmozgk8Dd01mPJNsLhyDWqFA=", "gWMTMrKSkH2oCb3FEQKzbHa5nfpgkzo0ZwapTKuWEzM=", "pPeHTzMAuUVXyL1z9Uyf80Q+UG4giwfbAgEsUZML7N0=", "5VNg6xFUC1IViQ+qtfIb7MJeJ8fRQKFOxJW+x6RfHJs=", "Xft4rhJgKojdZt7nHglMpxmu5XwfZQJcJVzj+D/Yolw=", "XZkM7aVAv8PNkEAri8ZreC11Z7AHsv4ssRFj3z05CzE=", "w0aPjdAE+jrf0tJ0FZBjxYFlLws+hHQ8F8wfsyZf4/o=", "LF6vjfZZNRLTo035LvSgNOLwiL6N6lASxXgX9aCNVug=", "7VkZYcmTIBHJM3NOdj1WOdpL725N8MJfcK/2VlZLIJM=", "Ul5M1VZ53R8qt5W30gj9ufGrSlLj5U17MI6BU01SGbE=", "neONv9XEkycPq51CLxWDI4pcqpwIGOgE/jtNtNeA9Nk=", "IcM1ia4UyU9AetzNnRBWS9vVcfQMLHfIdvKutBhh1tg=", "y2W8odyqF4wvIUZ/1fTOJjW1XsUXVN8YOkzZZbWz5PM=", "ISlvXF4QOWVUb+ZfRxd+9Qo5jTbPQ6hIgpZi5T2qaUg=", "F33y69lT9yDNqPdoq1vmdYrFKmwRgPhRHsZ8HHF7cIc=", "PjjEDgd/kxahszRJrEujGtmj02s8NHDN7mLVDijbgh4=", "Ecrske+wQUrGfi4X6XeoQeuanmIe3dB6NOnZ7rgiBC8=", "UkFSrHgSbpYH1u/b3rNk7VB04hApP+JoKT37n69Ujzc=", "qtdLU56u0DdQMKQ3oxInbC3Dtt+oXLyNy1SAd4pG5Gc=", "SoiZ1a0ET3H0dahnRSDhZhLJzietwvfiIEvRstt9Jmg=", "SMJzAlH6iQTE8o6WQ41kg+Ra2Upz2apukwcgq/Fga8s=", "+QAYpH4l+8Huo2nNoero+WJF0Xhp45+OgvHYXd3Mx8A=", "+TL/CnegQjyr5hZGmpmZJAUy/xoj48ecaKnT5snMoFI=", "cNjoVxedBiTfk/MjW48Pa6sbfSCAx6zzvzLwp/cAXU0=", "M5YAAwn7qTQN72Ps6fqvWZtU/TLs9O+/cIuD+SdQhnM=", "9Oj6CdE+OHrY1MUZKThnTaYNsMunILzG8Oir6e+Tssc=", "oAABOkUPZuwhKpJ471pGWrRl7EqziIKcfcd/e6Aud80=", "MML3XhVMlbRy6loQ9VujYlJmTdIX1Jkvka5WFcpGJfs=", "o689Xr8G5luALleyPPHodkve3Yxqtj8Jnw8FzqD4BCc=", "MI0oAPpywCTd/3kQ9CAYMTxUW22FAX3odxHiyAMlB0w=", "YVNjDM421JXLN2PIQRzDBmcaIuXe/Jn+7QZ1CU9s7+s=", "+82D0FjQzGhlYzFj9gxgwTfFoOu5z7dDpf7mHmPhXhs=", "pAOuEtGqF241BG6JHK3vzLPDMo4Oyul6smiR6smO3f0=", "CgXVLhXPIQ0lcUvazZYI4SQdBPCsOvfRUh2k+PAjUlA=", "TMs5yMB8xRPbdmyy3AU3fT6KyJqEYdGRhNFF4fE8WKw=", "9PkaqWzaGQNDTO4+UR5E6ipgQ9pv7aokPuuGmdcm9aY=", "74sezL+OOUNakB08wz7knY7q76wwPaPfF/OeEBml6Ik=", "eQsObyfCdf9eFgyS2DcRSnHYPhnEt1251tWDfRX9b3g=", "AL3W4E5nlVfOs3HB4KZjrojsAMfmQFw2JtI2xrAJcOc=", "eMfBLDtkvjss0MyjNz0pYmKkPslblY0DCfjsQWP3sgo=", "jq2G+uRXbzvzQ2HbaEwuPEEFD5m98LIu0pC5zQsAvjE=", "XJxlCyZ+UJVNnRreBh+Vq9ysgZBK5UxXWJIax1MyZgI=", "XnY9aeA5kbD7FdIBwi2QPEblQXrfIj8Kt7lzazyL4lk=", "vObNlHFwZ4UeduqBZo64pMJGx7ZSRMZKAg9BC3kuJ7U=", "JLFkcRxROFd7p+Utk2CTvYpdVSB7Q0a63yO+DCHnnUo=", "MGKgFm7c94/vlRSzJwbLvsl3Z7o8upaf2P4dIa8vUCY=", "ox4zWkh/xQt2QwQVpMNzEuIehk0KIptz/e3j1kqqZiY=", "dfayz5NAHzbHyPTnm+5e8LQN+paad5meQtgR2pNnc3Y=", "0QtGSPwC6WnSzP1PfO1/nCMP4a3ozlTjmEq233r+TuY=", "3+0CaH2D90qcS44nywzUpTQaO0OElevr0lbDyk1s9xw=", "SSAcmw2M1HfciwRLiAeBkD/FqF3X8xmMrvCFRIwgkf8=", "bsiymTUYY4hCqxHYlSj9PBRBiaI6FY68ktyr9HjKfZ8=", "bwqJ7EKTbnzU19lGrtIeJbPQiLWGWO/aCjbSELPm+xM=", "R0cntRES26Y8TOz+xuOXBeXVAqx9wDm+zk9oxuGnFx8=", "vwRPe0qNuSi/rJhj5PO17Jpy4d8RBVkKifD8Hgp8IeE=", "25b56zd9dXSM1ALq7koHxRsmvS8QXBu5FddPJcMB054=", "7EX61C/6zA8O/K5yInLMcxrXrM1DolIIrJ2FOZrBFrI=", "Bf4fvAp+n//rRtoM34Mhw/05R716Zmde10Yng+DiHg0=", "uUYFnDPPi5pjARmUpSkC3K8c6zyLGK7Mr0rezXfHRes=", "wN5uO00N8bZutUcp6SygWA8gOil3mPo0T1GhGBeLn+M=", "wnjre3mjVnP23F4TqXuoEB7tlmpSwYK7dJYmN7QIO40=", "3Fzp8/OP5R5hCEIhFjRFZgELpN8Uqa1zubdUz2mqRXI=", "1sMmVb2pYSkZeKNTwdLPU9jp885XJ6GO6fNEMofmhu8=", "/iBtzG/M4B17SAhmnqbMN+z+JRCSVaXCTSc7JeI4ZJs=", "C6QyqIHyQcPxJ6PRhQ7DDidaiB0kIrsopPD4Pn1qmxE=", "HscNIYDYIfLq8GArfAsiLmsTIFdk16m8vA6D6fHjM4Y=", "iSCOkne3mNmbUPPXJf97oAdN2hupePTOshxSRaIPVxs=", "HnM9tJ6eJ6MhodClVqZbi/E7BMYSy7CrgxXflCQzSJw=", "Kf67kLG5e9hR/9VJ9XCbTlHTfWyLEEcXQyLXsjA011g=", "IqwNINKjmzlz3gM+X1Y7w5lpSIEvFZZ3068fqN4kGQU=", "71Sg30Mge6fAAnI0xY7p7QHZ0Pr66Ah6OvVz8f5+mwU=", "2ed3GaKsXy3a8RRnQ0fmJlAGqui/vZbWiIKZxllKZ0A=", "IoU7K0OsZgU5StwaUnsA/cMPiDeBfVp+RAgkh520HjE=", "urgjelVer/xTipyslvSreQZmpMJZg48D/SFY40d8qW8=", "FGw+bzXVecF+YQP7jDDt6Pe2F2+CXowJHIOLtbje2vU=", "hINl4Hfu7Shz/9ntIjLVUo3ekSf2NSNGb/i3FPYVRHM=", "ceio92aI9sya+htawprFBjJ6LFjH1bul5cbdqY5l4RI=", "5WQ4quNJ230pz9VUv+ZTxop9Wo1EZOsPEkkJR7iuBhY=", "tCcRgA4/VZ1ab2fzwSiqlJpeusb6eAUwweLFh7MXWPc=", "tmzxhL52A633NfW2+FwFliFcB44MamhM676KmxmdO3E=", "fKmf5gC/0RMI3rxE/+Fe1niLLZxg1PwHdBX4cU+0KHU=", "4xYJFZychfMWsJZ2uXQIIPjQtwKecJB1ccifyWh4Tm0=", "iminsH5gVhRfPBxxBp5pu+rSN209s/1J1sJNCzSPcPI=", "kZthEBR7OSVd0VzjLx5tVbIz3p6Oe1zXNlCQ0qaSjbo=", "0kmfXvazuQxPT2vKMhMwMLrqb2yGsGkLr0vZPDL+Rr0=", "oAXOfLPjgt0tw5GNu1j5mMOiyfrmpl7lhmLq/uI1cG8=", "9O8O3jz0dQmpoSf+DFSjGuXKwmkdKQViwmZoAL25fPM=", "TPtQ8nKH//uXHd3BTmLoOVrJh4EVAdWI3AHCCR0nrAA=", "W+khrIOo5OYJxaRM4YQg4p/GPtU1+RHblUF6ynM/6Aw=", "Zg4Y5yic8ddGzXKJ+iuM8fmTYX3SSeTdbHpUFZEATHo=", "DsZtE9g0gVipGrK227Y1JltxVcNPoDzRy8gv5uTMI84=", "iKY9GYmG9HFcFrSVpwC0Czf/l78YYWKreGqsgzdWI7k=", "udDjIJAJBuV5XJme4J42k3FtVA4QTEErip3i9XKAN3Y=", "H2iWfm/jF8er7pCMyPfBguWfM+e/SonWdXeCOnWusVw=", "MmXhfTYaNNjYAk6uZ/hqKpIAJbYt7m4AIoCzEk9Lyfk=", "sq3lSZ1pLr5d5LA2ERJ8fjRRlWJkCAu5DifgVd+ESH4=", "DDibMy1dY+VmxhPyVTgLWcmDbvtDTg9DbyoJAomafQo=", "8D41TCNyN2oLTyKy0UNR3J/1YtU7cDCrMFcgZv6Ygrg=", "OAuy5BMw7G2z2Wp6vnjB08nWS6ryQwsM/oGgWJNabrc=", "iSkynvRT4rB3HOLM63KHAqtCAL1cq2Thb/HSxLlnXBs=", "lCQeuLJApF+L/19q/uMgTh7IishGuHVWJFhwQCOJkqI=", "jt1Tf7z8ssy/CryC2MbyVPnvTU7sRZCsJ4x+lCwfPbc=", "OO556Fu81EkdmHoOS0KoT76rPOLGkENudvNMegWaI9M=", "Aq43r3fCX+HGmiKhMVm14HUE+WkEWkFrNo6P4VcTbrk=", "uK31aFsZc737cCBJOqVk/RNWwVwOOmu00zVODeTelBY=", "/XFgBmK5cPZQ82HqK9lzPaJ+HUx0wLzPPNGec2Q+01k=", "CjVz8SRnIK8YeV/vNzsM3DzmzOqnhEdo041gAo+dnaQ=", "3qthsJ0C9vv7JG/fyQS8YZRB4/Q8BVRim2jZpwWVcFo=", "ag/EtDbRiRma4zgzVs4FtVK3Nu5uaH/OflMIUVqzCdQ=", "QXZXE/27n0dKQ1nDhY0Ze5buPHgix3JxzhIRQ1AZoUw=", "1hgdOggo5vG86WrjOD/kec7cyBxQD/Ntw6BNtKDdops=", "A+UV0kKxh4N0GFieEdaWAp45TSIh+av6cr1ZW52iSVQ=", "v+pDD9ll9RmuV/ty2hKNx73mQU7fKqgsjLckGYondKQ=", "qUuOBbP7cPVXabrrz4I9TnQQYPqSBXEA5dWd3Vx8Ws4=", "LqIvvrxYaV3mgZFlNomtNlDhLMFGPSD/ELSk6XFkqOY=", "Uehu+f0gszHUHQPB6zoI8svNTNOnkC0FIkxMpp2if+M="], "CachedAssets": {"dO5R91H8BF0ERQNaCrTmplLf+/5sGLMprvfecE63P9o=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\58gft4q2o6-{0}-tswichycbp-tswichycbp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint=tswichycbp}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.Development.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18s7itl1b8", "Integrity": "X/+i+NVLERSsvyIbCPwVbpCnFJxSjGtBN0hXRQ/QJ3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.Development.json", "FileLength": 110, "LastWriteTime": "2025-08-13T06:09:24.5614915+00:00"}, "InoZllr5mETBb6nMPSKg6QIAyESdpH0b6W5JuG/VHUw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\k8cprpwkws-{0}-tswichycbp-tswichycbp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint=tswichycbp}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18s7itl1b8", "Integrity": "X/+i+NVLERSsvyIbCPwVbpCnFJxSjGtBN0hXRQ/QJ3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.json", "FileLength": 110, "LastWriteTime": "2025-08-13T06:09:24.5669951+00:00"}, "Yd8qrl1tHViAPTYeIPi+hkQKk1hr7k87r2AfQ8McOfY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\n1ecasqnz4-{0}-md9yvkcqlf-md9yvkcqlf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tu2qkcmwci", "Integrity": "C8sFFlTouzLtqtRyeQRNMFLPF4W+BCiy3mRTsrDEK1U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 18006, "LastWriteTime": "2025-08-13T06:09:24.5740159+00:00"}, "+FTftDRHryEP8DkjyCZ8M88d4eto1yJDk9yQe2s9ntE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\s4af90zfta-{0}-mnek33bc45-mnek33bc45.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "ZeroKnow.EBS.WebApp.Client#[.{fingerprint=mnek33bc45}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\scopedcss\\bundle\\ZeroKnow.EBS.WebApp.Client.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mzo2px474x", "Integrity": "/jfm4gFZpQGbhj5EHhWSxXQNH38POBw9qHT+7gUzenc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\scopedcss\\bundle\\ZeroKnow.EBS.WebApp.Client.styles.css", "FileLength": 109, "LastWriteTime": "2025-08-13T06:09:24.579023+00:00"}, "fWwvaNTsCVKlnS61k9dH9CKRkYwWs/ZfZ9EH1Sf975A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\8t65xramue-{0}-bvu82j4ad3-bvu82j4ad3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization#[.{fingerprint=bvu82j4ad3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rn7ywsb9ec", "Integrity": "uaQ1LS1jmFmZzh352GTAoJXC27d1XRYZDoQEPFlsiaI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 17687, "LastWriteTime": "2025-08-13T06:09:24.5875298+00:00"}, "B7T8OVrjIjd6AdhFtxGzFRaVDM1r1LR1zhG5G8OzAPI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\z4exqc4shi-{0}-ptfrz3fits-ptfrz3fits.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components#[.{fingerprint=ptfrz3fits}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4u1wmazeum", "Integrity": "TL/RI2mglttuA23zZN+3As8ND500F7dvLyGQn7Xe1lw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 132526, "LastWriteTime": "2025-08-13T06:09:24.6060805+00:00"}, "NGWYBFrRuR/n/XgUSC2Rpiw6MCjg/Fzv1TdfQ9Fs93Y=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\g1fzn7itsz-{0}-e4o7p51zuj-e4o7p51zuj.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Authorization#[.{fingerprint=e4o7p51zuj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dd0ukx352f", "Integrity": "n3oI3tYbfou0oVhql+iLNmn8YMq5YyntP8rmtyAJbN0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "FileLength": 9860, "LastWriteTime": "2025-08-13T06:09:24.5634921+00:00"}, "x+5Zjlzu2ShYLdTKNjTY+768THrsCNua32lHc/2N3Bw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\3fzy8q7d4w-{0}-73oi73dvgk-73oi73dvgk.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms#[.{fingerprint=73oi73dvgk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d75ticaql6", "Integrity": "meE31nuqz9R2oYPZOEtKSHF3XFPlsTCkYGsBqmm7dFo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16299, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "jLjiHtzFKkWmTkPCgu6P8lc1mC7KDFLyHaX4R7fpp5M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\rlot8jbmoj-{0}-pm8mpy5cip-pm8mpy5cip.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web#[.{fingerprint=pm8mpy5cip}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21fcg1h6jt", "Integrity": "Fhsan/uHdrVJPrYtZJ7AdY+/Olc7tnGsiQjT8SU3N6E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 70894, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "AYDOuSWjQRHVQy1/waiknXi+kPLCK4NHQp/KLsg9Uww=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\z265ud4v46-{0}-2yt2k81j3x-2yt2k81j3x.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly#[.{fingerprint=2yt2k81j3x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o5956j927o", "Integrity": "nL+yTALP41d/hR9znqDm2fd+rDWjSjk6ssnIeIyVp5M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 65863, "LastWriteTime": "2025-08-13T06:09:24.593555+00:00"}, "161leAbtUpkK1yEkzNx7lT35U7GZMsdXZIsshJOu8js=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\j1nj0bljxf-{0}-hlbn62k9y7-hlbn62k9y7.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.Authentication#[.{fingerprint=hlbn62k9y7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.Authentication.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "up1onx6z78", "Integrity": "TiYuGetkzqSEAuwCT4h5mjX+8o/e5C4mtWO8tIrtjUA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.Authentication.wasm", "FileLength": 38030, "LastWriteTime": "2025-08-13T06:09:24.6005778+00:00"}, "leoajpcmje2PrE36IfzaclOgihpBoRPVrFBthx1Gjms=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vhgeetol6t-{0}-eyher82q7e-eyher82q7e.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata#[.{fingerprint=eyher82q7e}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jj5cwd5cf1", "Integrity": "8ZT///Rb87GeFxRs8ownOt5YfUiDt14xFuC+fwwi6eU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2408, "LastWriteTime": "2025-08-13T06:09:24.6015772+00:00"}, "aVdpbL7m5bCsE0TD9v8W1IrQ1KHgoL7Xu+PNXXs8Ef0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\rr7a4z0g04-{0}-itm12vk377-itm12vk377.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration#[.{fingerprint=itm12vk377}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "47xmptjdfp", "Integrity": "kA6glfdKoe419kfVHMWH24oM0VrZlbfJ+1poRVIX8qM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15536, "LastWriteTime": "2025-08-13T06:09:24.5644923+00:00"}, "EHH4FmFPnpu/1ATkNV6Oz9wx743fFHGK8GNz2Zu/Wto=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\3yrm44lql8-{0}-8ewlps0g9m-8ewlps0g9m.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions#[.{fingerprint=8ewlps0g9m}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dow44okadv", "Integrity": "bSJ+SFJ/bScm11NbnU36NJhWLFBQNJOuoTFJ6qLV9LU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 8297, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "DhI5mTJsgUXlDRu4azTn8rnGwDrD5wHsWd0LQagQcsE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\z03dszrn1h-{0}-yr6bnfroy5-yr6bnfroy5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder#[.{fingerprint=yr6bnfroy5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "96l2cly7lv", "Integrity": "T0gWzb1BTmawe7uWGA2eEKqwVDRkNOmQW/QDVgSF6Ik=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 14557, "LastWriteTime": "2025-08-13T06:09:24.5780199+00:00"}, "YvM3tgF7SP5F1d+r7EemqVDNFij+nwjyIYKHvJh1GrI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\oaq38xm4n3-{0}-6zj77w12m9-6zj77w12m9.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions#[.{fingerprint=6zj77w12m9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ppjsgesydd", "Integrity": "M2XhdpebalDM3kMTRkCMNCaON233KnIWU/3zOKbhYiY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 8228, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "5zKcM5f1SOM2xEI8Yj0k5M81ZdhwCjor9upWhSj4TO4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ibs73fqode-{0}-rzh7ctjkaz-rzh7ctjkaz.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json#[.{fingerprint=rzh7ctjkaz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "419z5k0n7g", "Integrity": "iDIDniCwm7L8G5QCsYYo2ebM9mzT0RTsYpwcuKNKv4k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 8040, "LastWriteTime": "2025-08-13T06:09:24.5895502+00:00"}, "HkH6YuAawMmMuG2RHyLh1Yn63CQrD6khs9CGUCUeuIc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\2rdtolq0jn-{0}-v66dtpac4v-v66dtpac4v.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection#[.{fingerprint=v66dtpac4v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dkwoqybme8", "Integrity": "I/OMmyZYJZeCzJGlYBCulvwLuLnpQzjuMyQeOFAYZUI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 35425, "LastWriteTime": "2025-08-13T06:09:24.595555+00:00"}, "Qekm+D75Cy8Qn+apdjYiHIAdOnTjJTRuQckzNxKnLMU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\nljhi7t2bg-{0}-apuz8nsfml-apuz8nsfml.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions#[.{fingerprint=apuz8nsfml}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pcx65pklbi", "Integrity": "WZf6gUcVZKdt3yMmpALH0MfBLIPdkDDh8GWnoPhvOTc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 21605, "LastWriteTime": "2025-08-13T06:09:24.5654923+00:00"}, "dvVkKnP9K4kPhzkSh0r71/GqET1xkKt6XweqbyTU2SY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\pe89m5fh0l-{0}-1bmpr6w9dd-1bmpr6w9dd.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Diagnostics#[.{fingerprint=1bmpr6w9dd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jyjyxjvf8d", "Integrity": "izbFAdNl2kR3p4dbfThla8HRGmCuPsSkPcQaxMNdSL0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.wasm", "FileLength": 12345, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "/dhDTHicwK7/psfgevxyFVrlHQEpP5zf8Fzza+d3HHU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\naqbf7eh4q-{0}-2lb9066vyq-2lb9066vyq.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Diagnostics.Abstractions#[.{fingerprint=2lb9066vyq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8do5k2zqv2", "Integrity": "nJbCN3+H0TIbr0tAbg5kpk09eMmOHbse4zmuP99ti1o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.Abstractions.wasm", "FileLength": 8786, "LastWriteTime": "2025-08-13T06:09:24.5775175+00:00"}, "vfBNsDLM9Bx13LYcko8sb/m/gPNfPjwPlr5NE3TCtGM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\pz9zp7w1cy-{0}-8bt7as0i9i-8bt7as0i9i.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions#[.{fingerprint=8bt7as0i9i}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "73xrqg8xww", "Integrity": "QzqHZH6Nf89y1+mYIsDTZOB1Ms5ojwvpdSu5uRcuaJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5629, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "svDe+iWl6Iicbkptm9huac/tOEI1O4Rtx+dm4a4aCD8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vsv0op328c-{0}-ily916jl2z-ily916jl2z.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical#[.{fingerprint=ily916jl2z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gq7399yqxd", "Integrity": "GKmvFiIpQHeQoAwpVFfbMZhtIT/jBy/+c6JxVLcoZO4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 16896, "LastWriteTime": "2025-08-13T06:09:24.590555+00:00"}, "kcWyJ8Ko2/EPwcFChpHkHwn+qOTVwxiBuxEIahk2H8U=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\d8u97go5m4-{0}-sdsdr06lyk-sdsdr06lyk.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing#[.{fingerprint=sdsdr06lyk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c6bv30em72", "Integrity": "rHKhWpD3rpzzadB2BJRoKYlTyBArGwzfeKmz3SYbc2Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16422, "LastWriteTime": "2025-08-13T06:09:24.595555+00:00"}, "spdnQ+rP9bJYf3p8sCC8QDlTceTC1T43y+aK6uwHVvI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\w8urpsu9uj-{0}-l2lanjn1yy-l2lanjn1yy.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Hosting.Abstractions#[.{fingerprint=l2lanjn1yy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Hosting.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u51ovg8tr0", "Integrity": "HmU5oQiDgAmoKhVmTPb98ITeNwg+5PeO0V6PqeabLuo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Hosting.Abstractions.wasm", "FileLength": 15448, "LastWriteTime": "2025-08-13T06:09:24.5654923+00:00"}, "gHBLKYDjxD3tO/mtPBmDdNrj25X93YDEpe9Zj8dkSQM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\uvgjtsjkr8-{0}-1gmjxv0m8c-1gmjxv0m8c.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Http#[.{fingerprint=1gmjxv0m8c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wcwsikvj79", "Integrity": "MJscU49SgkOIRHiHFmbUovlPmZyJe4NPJFPlomQEgK4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Http.wasm", "FileLength": 33645, "LastWriteTime": "2025-08-13T06:09:24.5760148+00:00"}, "hnHl9ZCPNBfFij94BXbFVrWQYXN3a1difIry3qfl5KA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\qk64t4reoz-{0}-ul0xzjnwdm-ul0xzjnwdm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging#[.{fingerprint=ul0xzjnwdm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "94izmhvvny", "Integrity": "ebCNdlTO4dYFeZ4XmQTtTTH8IBgEwAGn1UY9W69jRXQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 18938, "LastWriteTime": "2025-08-13T06:09:24.5820231+00:00"}, "GGFhDK2HMazmlxPbkU6qsvLsrmSju7mlpjn8WJBTQ/g=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\bsw5ivi4xo-{0}-nwxyu3e2hm-nwxyu3e2hm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions#[.{fingerprint=nwxyu3e2hm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zd0w7usuiu", "Integrity": "CVdLNG4s96Guh/RQeSdL5V//VcDth8QDYPT1iXj3AJU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 24537, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "vnCtxfX+6h/4N6gNPWp9+lW0DFhMav4ghNBJHThB7EA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\wi6pqzflbl-{0}-l36scmr1xu-l36scmr1xu.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options#[.{fingerprint=l36scmr1xu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdndjvgz3w", "Integrity": "l0axP0Jtcq9s7NNOa7gZKZP0HuTGb3hAtiGMeJGlSPY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 23736, "LastWriteTime": "2025-08-13T06:09:24.593555+00:00"}, "bvdg5snHksL58VokBp5ly6J+lfyHOPOujfnTT1IV36E=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\g7endshhvm-{0}-zrlhdwvckr-zrlhdwvckr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.ConfigurationExtensions#[.{fingerprint=zrlhdwvckr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.ConfigurationExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eyji1qzr2q", "Integrity": "epF63jDsOdnAKgocsFzc8hDwhreQ2DeXckhVDcxF3PI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.ConfigurationExtensions.wasm", "FileLength": 5446, "LastWriteTime": "2025-08-13T06:09:24.5975735+00:00"}, "FWMeuyYQ/4bLD3kuY8U0xaX6f/KK1/Oui/RKszegi6A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\bg9ysvo09w-{0}-358c2dzezi-358c2dzezi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives#[.{fingerprint=358c2dzezi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rwvm9cincv", "Integrity": "9Xua7toR182nfeb/sTiYATwjO677ueIPZJuTadK38J4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 15236, "LastWriteTime": "2025-08-13T06:09:24.5654923+00:00"}, "VVBqFQkV8DQP+y/dYj26SSqT3UW5xHkM8FBFfNpMzxw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\81tklvsd2q-{0}-ybaiolflut-ybaiolflut.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components#[.{fingerprint=ybaiolflut}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7j3vzrmjjr", "Integrity": "XW3AWShm12SMmT2UV3r5lfSSO+0zJmAxAc7lfgHj2TE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.wasm", "FileLength": 745840, "LastWriteTime": "2025-08-13T06:09:24.6369623+00:00"}, "VKl9RcvbEK+DhFQ5J/a0dq3tRQFyHtNZmLQcd1RQBaM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\57o2qceic4-{0}-zq9m49vz6p-zq9m49vz6p.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons.Color#[.{fingerprint=zq9m49vz6p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Color.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fq3xlhqb64", "Integrity": "Jwphg4gMA98zDkgg+JAqo0KvKgB0iWZp87bsG4IeP4c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Color.wasm", "FileLength": 359867, "LastWriteTime": "2025-08-13T06:09:24.6952581+00:00"}, "Y3HNaWzWIWk6v+BQppJwh76/P5SyR+Fu76DnJMBQ6aA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\imeimuvg47-{0}-xu69rm84hm-xu69rm84hm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons.Filled#[.{fingerprint=xu69rm84hm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Filled.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ze69jqdqu8", "Integrity": "u3OWOx7e9WwUYwTmK8ofGdArMLABeb9goZtPo9/jAfs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Filled.wasm", "FileLength": 1509606, "LastWriteTime": "2025-08-13T06:09:24.8559694+00:00"}, "Ylyks9pe6Sp97vuppLD4FTEx5uOdVwGhTgE+arh8Ha8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\nq3jsz6th4-{0}-0e9mwdlr03-0e9mwdlr03.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons.Light#[.{fingerprint=0e9mwdlr03}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Light.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o9htacdrer", "Integrity": "rIYjmC+4nzZ0m4WfFm+Msq3ESrHqjtcCusLKeB3Iupo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Light.wasm", "FileLength": 51628, "LastWriteTime": "2025-08-13T06:09:24.8625821+00:00"}, "iKSur2gvQZEP1k6sEdiV2Sz2mJy0UoPnBMXQpA0IpKA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\15xunyunqk-{0}-4xozf64wa9-4xozf64wa9.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons.Regular#[.{fingerprint=4xozf64wa9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m11349pjq4", "Integrity": "CsDphMhUp7MlbPLsqveJ3YJXMCO1aTktLtmADhmUBRY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.wasm", "FileLength": 1713828, "LastWriteTime": "2025-08-13T06:09:25.1486336+00:00"}, "+ddsgJka+n4G8aCjjlWUgZ54eA53V4e9qziZcNK1CrI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\yspzdyt2qe-{0}-73zzoh8l6n-73zzoh8l6n.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons#[.{fingerprint=73zzoh8l6n}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l9yf8r5b9g", "Integrity": "x0CH3qjTU7qVahEdPyva6Ce8s0qv6umX9Kka4QhF5fM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.wasm", "FileLength": 10066, "LastWriteTime": "2025-08-13T06:09:24.5644923+00:00"}, "UBo9rNQdy8DaEaz/iISIBeMnLgFwicRxR0sXcUm94DM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\qs7ozt69b3-{0}-nanjlpvyw1-nanjlpvyw1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop#[.{fingerprint=nanjlpvyw1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7qcj5gomn8", "Integrity": "LyxS4shThCVKPTN9Y4FuUKYqLO+W1PQDpSB9TVPMIE0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 23587, "LastWriteTime": "2025-08-13T06:09:24.5740159+00:00"}, "dHD3iB26G75JgN0xfOptKnzPC6jVTXDiO9F0e+2Rk+A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ya4bywbml6-{0}-btoflm7i7s-btoflm7i7s.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly#[.{fingerprint=btoflm7i7s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qi015hggae", "Integrity": "Hj6zZfJGvxm2jYwZRKFOax/wwnOlOmm9TMZETB5pJ1M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 5722, "LastWriteTime": "2025-08-13T06:09:24.5800229+00:00"}, "VfGqZ2TfwbYKTDNlanxapJ5ad9vayHEIQGVSwunMqr0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\xozrar89xd-{0}-9gws8s7zmg-9gws8s7zmg.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp#[.{fingerprint=9gws8s7zmg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "py6av3y9fm", "Integrity": "P/M2iqA6lHfoDf2g5SbIpQDdaLOVaDR/q2/oW7MhNnQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 130649, "LastWriteTime": "2025-08-13T06:09:24.5995777+00:00"}, "Wt9onCva6Xrk37ZxmyZpp+6rtJwmxX724H47t+3di0c=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\p6p24vxflc-{0}-hev5t09xbg-hev5t09xbg.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core#[.{fingerprint=hev5t09xbg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4n9eg7mvjz", "Integrity": "sCtt4cqInuOPBk6odutMf3VkVJADHY+0shkNhGlbOOs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 167038, "LastWriteTime": "2025-08-13T06:09:24.6197324+00:00"}, "jMxeDrNLq7r62rRCRLzD7Jw2sh0t+mgRui58o0ibV0g=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\e58q73oxjr-{0}-wy3cb00pkv-wy3cb00pkv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic#[.{fingerprint=wy3cb00pkv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "scj8o8xt75", "Integrity": "/JtLDfFPSgByHCP3+RQj6m3bTzQex2D1f3gQLqqJseM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2847, "LastWriteTime": "2025-08-13T06:09:24.6207325+00:00"}, "7tjzpVgYpl/yTq3j705QZyK8g2XSyhwVDh+6pK+CGWw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\i8zzwev4t1-{0}-nt18748s0w-nt18748s0w.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives#[.{fingerprint=nt18748s0w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4hb8majtfr", "Integrity": "6GWFM/FCdveAb+NLIBiv5CHcQo+TlKoo8kIC4cgRY9w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2183, "LastWriteTime": "2025-08-13T06:09:24.5644923+00:00"}, "8PlTz5zd7+hTzyNuEaoiZWAj5pjPVjDRXHY7f7C9xgM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\96uzfo3199-{0}-ykr6iyjchr-ykr6iyjchr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry#[.{fingerprint=ykr6iyjchr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jz3dn1w02f", "Integrity": "kY8OYpvuLwupBxluaednsL+DUmA/TjvP7QPVD4eHPD0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 9076, "LastWriteTime": "2025-08-13T06:09:24.5725068+00:00"}, "GOnlvQevfteFxWHTwC4myJ9x3LVPns3l76ABaVGJoOA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\nhainmd5cd-{0}-3h1likbfvx-3h1likbfvx.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext#[.{fingerprint=3h1likbfvx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "an<PERSON><PERSON><PERSON>", "Integrity": "9pJkqHFBC2NY/aEn4xr/olsZbQq3qUnBhmMHRafIAf0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2084, "LastWriteTime": "2025-08-13T06:09:24.5760148+00:00"}, "491+78BIC6dQ2qVH/eCipi5nDgJobugiCF56SVSDX/A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\tc9i9jtvbq-{0}-wt7n1r1ovk-wt7n1r1ovk.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers#[.{fingerprint=wt7n1r1ovk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bt8vkjq4tj", "Integrity": "oWFVFy9VEWrFIngDS1BcQ5N8RDNsOl7g9VJtwDnlheg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2096, "LastWriteTime": "2025-08-13T06:09:24.5810236+00:00"}, "jmYGalk0W9xe+yEZlydD4EAb0/p1poxvgxXzNiotoGY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\f9tmvsja4l-{0}-65adg6natn-65adg6natn.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent#[.{fingerprint=65adg6natn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kunn69262t", "Integrity": "fAJk4HUzbhFGPpIdS1vVKLO1B+m6dmwrIQ+WJHAi55I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 33892, "LastWriteTime": "2025-08-13T06:09:24.5895502+00:00"}, "gMc7ysLTq/M/RvrL/kLVWjJqZxEngT7cWDeLaUpMo/Q=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\3v672o85uk-{0}-dufaq3kp3z-dufaq3kp3z.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable#[.{fingerprint=dufaq3kp3z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pchm9zgosc", "Integrity": "p+kB8Ej93b/+yaTVL73H2ABvgrOzPSglfNIOn+U7jDk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 99000, "LastWriteTime": "2025-08-13T06:09:24.6025774+00:00"}, "IlMcCmnW9JH0JXZSBibLMeJ6EcKKp3GbEXpmhPztk/E=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\q9m82irh78-{0}-rxjrzzpp9g-rxjrzzpp9g.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric#[.{fingerprint=rxjrzzpp9g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i271zrfw6o", "Integrity": "Chlh7NJIKsZVEQEi3mauF4K7+Ag2XQEYrGbmXmSDsdg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14669, "LastWriteTime": "2025-08-13T06:09:24.5654923+00:00"}, "l7srGEAauqGTvx+/ucxqDbehCv6dP/Atn3aX2vGjfZ8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\64zsjbhmm7-{0}-grj2h3kseq-grj2h3kseq.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized#[.{fingerprint=grj2h3kseq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z0w83r6ocq", "Integrity": "I+NWd+jUUXMF+q520KQgVQTuj9ccGxGodS+E9J6qjGI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16270, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "zbGHIfs93Rmrp+4pobRY/elRZLMNzqaJQLUAnXMz8PE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vvtebh6tj2-{0}-cip8dbnu43-cip8dbnu43.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections#[.{fingerprint=cip8dbnu43}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hm3usfz5bs", "Integrity": "J+orUIlPbFK1X2/dr1RiCbxZNkeLvU/X8xzEZEJd5yg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 48805, "LastWriteTime": "2025-08-13T06:09:24.5820231+00:00"}, "4ePBbQSxxaFuFSm8iOCxn5KzAIPUd/H4I7ylIlMrIug=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\w76y78gkia-{0}-6hr3q9fx89-6hr3q9fx89.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations#[.{fingerprint=6hr3q9fx89}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cpb0eng3kn", "Integrity": "wHqo8h6XLUzu1hRcoNP4f2ZEjdNbX+NRlDyH1hZ04Ps=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 35435, "LastWriteTime": "2025-08-13T06:09:24.5895502+00:00"}, "LM03bVhZHyi7C01nCrfkpLHZk/tEWS3Ud+BUcuN/UIQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\psgdso1e0a-{0}-k6p4pn9w0l-k6p4pn9w0l.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations#[.{fingerprint=k6p4pn9w0l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fvul1sxofg", "Integrity": "dc+WIskOQ372c3WH8mrVXoVnq4W6hiLV+q+J9Vwy78w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2559, "LastWriteTime": "2025-08-13T06:09:24.593555+00:00"}, "jRwKN0xTHRMlv82GQEgvkaZ9959sVRjFfaye/y05XVo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\kihaaekz39-{0}-p61cj2koso-p61cj2koso.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync#[.{fingerprint=p61cj2koso}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l4kytqk22c", "Integrity": "m8Af4NxHofGqCGWp7u5rkm6N+mJwMvpdOZpScNAf8SM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6750, "LastWriteTime": "2025-08-13T06:09:24.5975735+00:00"}, "I6GZmVXLuDZzLjKAiwtKOByMzqHibM0PMNVQxJXmjpo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\6z5sdd8gxu-{0}-fea7hw9xtf-fea7hw9xtf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives#[.{fingerprint=fea7hw9xtf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3ndiyhvslu", "Integrity": "j0to8pJo9u8tYkxI2jJ3m9BXh0z1bAu7X5JyaW1T3e0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13328, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "FxH5liNMNznm8/JXJaSzjyLw+StB1/g3aVtIgFCj9Jg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\aygdka5une-{0}-etd3dkcep2-etd3dkcep2.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter#[.{fingerprint=etd3dkcep2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cx6fwic2qh", "Integrity": "0LjdKFSQvLg6/j5WGDS0AVy+InMoaw7mIUL4gc6q54E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 122325, "LastWriteTime": "2025-08-13T06:09:24.593555+00:00"}, "EZbX4w2ET7wl1GhtTkRajGrAibifBWEYvJ+EZfmpzlM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\z921vqe0jd-{0}-0lm42x51au-0lm42x51au.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel#[.{fingerprint=0lm42x51au}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "71y2xfbtch", "Integrity": "y2XCeq7N7+8MX7zi/u+BVNUVi1pQPxbtnO45sdbxZiU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2539, "LastWriteTime": "2025-08-13T06:09:24.595555+00:00"}, "KX2AstDXnivjn7jZWkZzyrjJgzqjAnsI5CcMqlXHEqs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\anj21dks3r-{0}-ex6vy58iyk-ex6vy58iyk.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration#[.{fingerprint=ex6vy58iyk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yobrqddsdl", "Integrity": "Dg5UKpAiYzX+NtU/e+RInLbTZXddLrJ71DIYnJWL0TI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3087, "LastWriteTime": "2025-08-13T06:09:24.5975735+00:00"}, "ZLBiPrCdyEr2IzUi4Ari++ap5dKinvoJV4lH4E/4glg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ibwgwtd9ml-{0}-s0qgw5psci-s0qgw5psci.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console#[.{fingerprint=s0qgw5psci}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ngepxhj1qb", "Integrity": "dyo3oJVxeBcrGbDhBGaL0dlgeZDk26tAe5b9zcQxTjY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19339, "LastWriteTime": "2025-08-13T06:09:24.5995777+00:00"}, "NqimYDgJcClr8L1uKZjDYGUlml5ikuqeV5wqgfxbMD4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\aza08xqter-{0}-zknkrutld3-zknkrutld3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core#[.{fingerprint=zknkrutld3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mih6nkjw7s", "Integrity": "0uqLpmtXW9OESWh7rLAUuLs5w4glbETbRIZyeM5jKjY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4532, "LastWriteTime": "2025-08-13T06:09:24.6015772+00:00"}, "WS8zD/Eg/KKs2tWjGxgaN++9ZEND31p3jn+qRtmQPCE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\m7svytl5ks-{0}-lu92ceoi50-lu92ceoi50.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common#[.{fingerprint=lu92ceoi50}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x8metbvzuj", "Integrity": "356Jla1j06TcAqnuDdiLYf3IG7unNcFM9JLNeiX9wos=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 374764, "LastWriteTime": "2025-08-13T06:09:24.6137229+00:00"}, "hiaz8wRnuJ8jI5yWlLj0oBf5bQBTLc7KtGuoI8Dl6U8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\elhy7bq5p6-{0}-2ddk0zm05l-2ddk0zm05l.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions#[.{fingerprint=2ddk0zm05l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xl098vkyuq", "Integrity": "q/AaebkCc/DLJV2T7GFR++Mw1ItvEVO6PBQjkat3AxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2050, "LastWriteTime": "2025-08-13T06:09:24.5975735+00:00"}, "OCsA0hswdTGvJC8FSlqvYtlCY+KMVuMPLBlxTJuNjJ0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\57xckq2kuw-{0}-3adg3wr0gn-3adg3wr0gn.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data#[.{fingerprint=3adg3wr0gn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rwkch9v50u", "Integrity": "JWbhFXCOjNHnf1DAX/noR7KB5wgX8nwvLhll1Bck+nQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 4984, "LastWriteTime": "2025-08-13T06:09:24.5995777+00:00"}, "Wu2c5Xd0Q6zaJx8ziM/wZd0ulbRmj+Xkp8bMk/AE5ug=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\5ahhucdyag-{0}-voyqcmzm7a-voyqcmzm7a.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts#[.{fingerprint=voyqcmzm7a}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c5gdiqwu0a", "Integrity": "AA1RwXPVK9QeXboIUu8X3IAYo04HSJbjRWMRuUqEmYg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2368, "LastWriteTime": "2025-08-13T06:09:24.6005778+00:00"}, "kAjfXdoX5DSqS1r8BZYV1pEA05lTEw8FAdT9mYFwW/o=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ojpse6rttu-{0}-tuw7jnpdtf-tuw7jnpdtf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug#[.{fingerprint=tuw7jnpdtf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "53sdlxfho9", "Integrity": "j6dsIWzaW4b1wty9nz0AXtQ+7l7BJJJjVMAFP/0l3N0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2261, "LastWriteTime": "2025-08-13T06:09:24.6015772+00:00"}, "6JYaCR8JFcBNEGZpvI3vE5GGUEE4ybfXYw73HLD3g6o=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\1aorrtblg4-{0}-orwvw7tsnw-orwvw7tsnw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource#[.{fingerprint=orwvw7tsnw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "430k3izu7n", "Integrity": "kKenqNSG1mn8Q5JMpJalOLMGRXLViALWmXBV4fHYzeM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 73039, "LastWriteTime": "2025-08-13T06:09:24.6097197+00:00"}, "6GqRZakMB/1WZMuJ4ywuBs/zaHAjABRIIICqvbAvNxQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\r0w0ototjt-{0}-i2nxqnh8ia-i2nxqnh8ia.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo#[.{fingerprint=i2nxqnh8ia}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n56t3u4fyr", "Integrity": "lSBfrJW/gEWdDE3RMoqQWXw4SYAnTe17mOXKQGU3CQk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 5092, "LastWriteTime": "2025-08-13T06:09:24.5644923+00:00"}, "22st5553KKy67fqXM6SUW11UTchg6smBBsWvHHvea64=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\bip3i01j7r-{0}-yj1m2auw1z-yj1m2auw1z.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process#[.{fingerprint=yj1m2auw1z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9nbpdmtg7q", "Integrity": "NRxRUbdikQNgM8zefCj20EhyRb480SDvHScGJxHLKoA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 16123, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "xKGiZJU1up8goPdpqZvYD/e+2ItBQkDAbAbbh0Yphwo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\jox0l4vbbb-{0}-9u6hm41m9t-9u6hm41m9t.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace#[.{fingerprint=9u6hm41m9t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fyp1qsyfel", "Integrity": "WZwiJb/2SorUA/OpVYbO1STlPG/liqyZxkLdoXfR0mE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7333, "LastWriteTime": "2025-08-13T06:09:24.5775175+00:00"}, "uuo1MEJoxi8Ye/Ew0o5w+VpRC8ruhyQKuqAmogpZYVs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\dc5r2mw9wy-{0}-670flx7nki-670flx7nki.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener#[.{fingerprint=670flx7nki}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gj7ifh3xaa", "Integrity": "VEbIw32HqPniuSbsXOgnb/+Prsfn49ZH4xHauexX7N4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9342, "LastWriteTime": "2025-08-13T06:09:24.5870262+00:00"}, "Azv9IBRucAqzUAALhtWIF3033LjY0Er7seChy/EzuZc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\2r5l6t1go0-{0}-2vqkac8ysr-2vqkac8ysr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools#[.{fingerprint=2vqkac8ysr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nwk46c1t8f", "Integrity": "1witXnaOtAks7FvyUm2StvA0vRUBxsE2u6TuFk5fU08=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2163, "LastWriteTime": "2025-08-13T06:09:24.5895502+00:00"}, "A1rKofGzYiiTTaCWhRqwEaLI+dOVyJPJsP+hBN9Z7zk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\l6ybehwu6y-{0}-n515vmkk2p-n515vmkk2p.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource#[.{fingerprint=n515vmkk2p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s<PERSON><PERSON><PERSON><PERSON>en", "Integrity": "IKuf1JVmbIDiPeKEl9n7d0wYw7Xl4wXDYy04p64VKQ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 20045, "LastWriteTime": "2025-08-13T06:09:24.5945545+00:00"}, "oMd10TKwRRSEtyDgQMjaoeghmuaYLliSCyAxi4wslOw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\nzfl03dw7f-{0}-ogliygwa1r-ogliygwa1r.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing#[.{fingerprint=ogliygwa1r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j2jkknxc2z", "Integrity": "i/3tOGuWeSBAFf8YtAMjFoXt3KIVu/xhuBnA/uJq0Q8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2485, "LastWriteTime": "2025-08-13T06:09:24.5644923+00:00"}, "a68C0pd+EFgHQzeM1BLF8f8e5x3L6Eolv2M65C46UCM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\43fmqoax5y-{0}-zk693pwck8-zk693pwck8.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives#[.{fingerprint=zk693pwck8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cj17u8p2wn", "Integrity": "iBE86Bo/43dcRhAbRDPII6PGWltu7+o1hrrcsSmm/LQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 24006, "LastWriteTime": "2025-08-13T06:09:24.5740159+00:00"}, "rLtr097Sz6mvRDZZEkx4hM5T+Pej5Vak3Pfvtk28rXA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\8bbl0axy88-{0}-wxhr0xa5hb-wxhr0xa5hb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing#[.{fingerprint=wxhr0xa5hb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ojg7c2wbp", "Integrity": "ZwGMejrYM837zRUjYQPTPYYPbLXhh1ZwyRdLGtLngRg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3830, "LastWriteTime": "2025-08-13T06:09:24.579023+00:00"}, "AdxyYcOr6Zw65aczraDy3TfYNDvJvDu14bpFwh6Enl8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\fgep7yzgsk-{0}-ipprcrczgj-ipprcrczgj.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime#[.{fingerprint=ipprcrczgj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lyxfv904ef", "Integrity": "rZzYrBJQvfgabnvslp2rr1Ylwc4Q3JLc3LAhNjBQacE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2422, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "qCNTNywrOoJlNoRUWip/uxDQQaPnQUelLmPZe+Wl708=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\z1n8pzvswm-{0}-okhe897m5z-okhe897m5z.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1#[.{fingerprint=okhe897m5z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "thw1m5qtlc", "Integrity": "P7tL44kDzlbU+9paVbZGTbXA94RkvbSYvmiRFEaMHhc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35105, "LastWriteTime": "2025-08-13T06:09:24.5915545+00:00"}, "QrY5mgcCtqcyL/7tkb4vo/fuczO+cM7KFF9LJP/cVbI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\9nmsy0qx69-{0}-i93u5bq4fn-i93u5bq4fn.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar#[.{fingerprint=i93u5bq4fn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6hm7p40dba", "Integrity": "dgkjBRdk5rLU0TgdsDKpupLea+4/L7Aq29DddzUEeCg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 10373, "LastWriteTime": "2025-08-13T06:09:24.595555+00:00"}, "2CljHW25ekqKGIa2jcdTlbmkm9QJjrzpYE6qxs6t7bo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\dwo0a7f9mx-{0}-x0sb683rhi-x0sb683rhi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars#[.{fingerprint=x0sb683rhi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "006wanlgvt", "Integrity": "oMOAdO3ne7ePS9Fv4zAk4AeFSgXe9ANkS/8h7OeH4Zo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2276, "LastWriteTime": "2025-08-13T06:09:24.5654923+00:00"}, "oYQsrF3ZHF6kCyEtX1KmMEXM/80pahA2unbsnR95HC0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\jzp1wb8xyi-{0}-o54lsqobzb-o54lsqobzb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions#[.{fingerprint=o54lsqobzb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ck0h74m2d", "Integrity": "91N6AqYjJHpnr7J/39CPAgxi7wXw16tPJihPAfZKUU0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2159, "LastWriteTime": "2025-08-13T06:09:24.5725068+00:00"}, "BjyaQdtJhC2JCNCaIZbCcAmmkVizf8kxt6ukJ4xvry8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\xjww2bjgnc-{0}-tde8zuw0yw-tde8zuw0yw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization#[.{fingerprint=tde8zuw0yw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jccpixzi4o", "Integrity": "Pc+9uh03752/oPPuV4joxzNE25G8aI4T20zs3/NA/0s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2242, "LastWriteTime": "2025-08-13T06:09:24.5760148+00:00"}, "GsJliBJnCheATZunJxqphoym6GFbWjRyVr0Ie4Ic+h8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\xb1kei323p-{0}-vx3bcge4ol-vx3bcge4ol.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli#[.{fingerprint=vx3bcge4ol}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "opw8fc0znb", "Integrity": "ZMnDiZaE1Q6nnHQHoDgVGCSiP4gOGtpmcWdEItLyE6Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 6948, "LastWriteTime": "2025-08-13T06:09:24.5810236+00:00"}, "b0TrnrF+ZO7xtpV48p6pd8s6ddIJp1cLrhdvNVKs/W0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\e8rw8mymn3-{0}-yhtj6e0w69-yhtj6e0w69.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem#[.{fingerprint=yhtj6e0w69}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q20yo0ya1x", "Integrity": "HhOdcMKOtx/ua27Bf3+EnP6NPIiAT1jfYyztY4//6AQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1978, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "Rm3xcZskon+fvnIlBqiorPxlr5+jwXvFdSNO1hbmZwE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\poputdfpjz-{0}-quahjtap8r-quahjtap8r.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile#[.{fingerprint=quahjtap8r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5nqh9yupva", "Integrity": "382jMaKFpILh4xIse+h0vgCZAY7c92UPOJYNGqU4To4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12398, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "hUEr0jWMqJffAZWuGF2yveKxY2BsejPZb2TdiBvJwy0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\7l0ju3idqo-{0}-jtaurxkbzi-jtaurxkbzi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression#[.{fingerprint=jtaurxkbzi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "juehst4pau", "Integrity": "leqO8cTmEDWVYT9pa+fvtLvjVhAtR17wCHfcNNdJjPQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 42932, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "aoisyiY4s4t3VH9zmLjQGzWDu+Ded1Q0MqUvGH+IaU4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\yu90cxj4sc-{0}-3yfpgyrku1-3yfpgyrku1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl#[.{fingerprint=3yfpgyrku1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "YtMMthf+4HE+hn/h2M3PKGA7sH1HnD7ddo2xd384vLI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 8451, "LastWriteTime": "2025-08-13T06:09:24.5770148+00:00"}, "f/hRUF8o7elos4kCWhdLj63jOmNhenbk1+Gx8O1ZZa8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ffzspv67vf-{0}-8nnv647ull-8nnv647ull.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo#[.{fingerprint=8nnv647ull}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cnjcvox7qa", "Integrity": "eJ05JAMdh501aSSXjS8rraq877jyDneE7vXhwgrQgiM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 6000, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "YbOCSu/RYYOoB1VWcaZnbNehTrkKcuydct9Zo2wM8gU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\roxlzif3uz-{0}-ir5j8vbyan-ir5j8vbyan.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives#[.{fingerprint=ir5j8vbyan}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lxessetpsk", "Integrity": "ZyGESdx2tAK7N0v+vHnQdwmWVg+NOZ57ersqZp78WU8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2162, "LastWriteTime": "2025-08-13T06:09:24.5875298+00:00"}, "kJLvVXBifDgwq/Q1KRLYM4+47+pWG8CYYL2fxgCgm8Y=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\zwge3ab66q-{0}-1lxrwwxsho-1lxrwwxsho.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher#[.{fingerprint=1lxrwwxsho}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "quqljnapdd", "Integrity": "zvWoc0bVL231LCjk/fW+Q4Jvi9HEqvV2pyv4ocUI6u4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8729, "LastWriteTime": "2025-08-13T06:09:24.590555+00:00"}, "OLXJgb5nGZHlnEpeaN9wwg4G4Use66eWbx40ZK5EkbU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\wfi02spp1l-{0}-gyxexdekj3-gyxexdekj3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem#[.{fingerprint=gyxexdekj3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rxe2iyf0id", "Integrity": "H8KQtGc1WhIMYjTCBmykIoaIKMBjl1xDciaxSL+kPss=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2280, "LastWriteTime": "2025-08-13T06:09:24.593555+00:00"}, "1DVgNnlqz43sCtux7dU6v7yjzkwNoiGgC/1L3R2JBAQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\fo4i1i3i4a-{0}-tsgf6g1ztd-tsgf6g1ztd.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage#[.{fingerprint=tsgf6g1ztd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9z8y2lbose", "Integrity": "9ceafAgJmEgeh4MsSrbsK2HbOcDY+Gdiad50w1fiyuE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 9327, "LastWriteTime": "2025-08-13T06:09:24.5669951+00:00"}, "cqxv3+M82E01GhAqWCEQZ6Qj6QIa1cVrF4kOT3wlImk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\jl2lh0k83l-{0}-j4sjofqyi5-j4sjofqyi5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles#[.{fingerprint=j4sjofqyi5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ipxlufb5e", "Integrity": "WBw9ULybZN1IqkgBL8CHqMekj5q9PDjtIRHKbyj4owc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16563, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "Jat9bH2UGTe4QpjH8BfilNhRdYpwmi233+m4g5HIGFE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\2dwksosxii-{0}-jiaey0kmyh-jiaey0kmyh.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines#[.{fingerprint=jiaey0kmyh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gijixpgft1", "Integrity": "uEczn81KUbDuKrP7jMv13I1irPYEDtuEn0NcUij6zhc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 30345, "LastWriteTime": "2025-08-13T06:09:24.5810236+00:00"}, "EqqpiZD9OJE98Dj+U3dg7ycZmYaU1XH4o3eO5wYJVGk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\l0llh7kyz2-{0}-qfh40ih8l6-qfh40ih8l6.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl#[.{fingerprint=qfh40ih8l6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3sux2pq4wp", "Integrity": "7f9FORmze5+pA2jlHegTfQhmg3MMkBi6OdgT+hgJ1dk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5543, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "LrnLY+Bn74wpOKeUmkKdmJg/kvZd7+sPLZRAsepThI8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\beke1dme0m-{0}-al6w1uowde-al6w1uowde.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes#[.{fingerprint=al6w1uowde}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "agd<PERSON>uwupr", "Integrity": "5tnvU6xyOtLzTdutD81vkRRRmf3Jh2jiZmyYflvJNd8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 11358, "LastWriteTime": "2025-08-13T06:09:24.5895502+00:00"}, "3xafzQuR4dx0jMqlvOpKHCC2roQ8Z1dN9wwowMz35P4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\x74whs6c0q-{0}-d0g45p3x9u-d0g45p3x9u.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream#[.{fingerprint=d0g45p3x9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glpr2aie2i", "Integrity": "rPsdyFmgq0/imC34GoiqGD3hseMx0FQta0LooHYaE9g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2190, "LastWriteTime": "2025-08-13T06:09:24.593555+00:00"}, "Lw9QonQ7RHWQOXaH2h+4hD9QIxH50RP6mp7e07/gQVY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\50z04zqcpo-{0}-2zge8rv4ra-2zge8rv4ra.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO#[.{fingerprint=2zge8rv4ra}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wp5isv3z02", "Integrity": "dFXM/nIZknEZVvInbxuL1HuTkIbOiXkLqeURsI8gyoA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2238, "LastWriteTime": "2025-08-13T06:09:24.5654923+00:00"}, "mV/t3dqAFwq2D8Krhy/UnMb9pnowYvSftwGBvu+YL0k=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\x16kq5oj17-{0}-2lw1u6ymmp-2lw1u6ymmp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions#[.{fingerprint=2lw1u6ymmp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xg6l0covpt", "Integrity": "AQQPRB3GgdAO2CswyzjMZd+jKiVrotxXuLGwzTL6by4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 213260, "LastWriteTime": "2025-08-13T06:09:24.5985773+00:00"}, "PRytrM/QEASR82Glh8N3yTzecHHmJaug+fZwxg2Dzp0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\bveb3z22ja-{0}-1jvfownmci-1jvfownmci.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel#[.{fingerprint=1jvfownmci}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilvxh1ytwv", "Integrity": "NtNPCSyHCN574iDUPgfqTV7YFp8/KGUgkNDIQg6n28Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 86356, "LastWriteTime": "2025-08-13T06:09:24.6080898+00:00"}, "q4+IvKmvBp/s9Ce7mk7iDBAx1/+d/aDfTW2B828muqg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\o0ba4eoqgp-{0}-mv4cb7fqwu-mv4cb7fqwu.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable#[.{fingerprint=mv4cb7fqwu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uv<PERSON><PERSON>ufq", "Integrity": "Riz4tUKdY++aWBxq26g+aMuOXBzbcfyphtldt3hyy5Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 20738, "LastWriteTime": "2025-08-13T06:09:24.593555+00:00"}, "ylSu1jzYJyBekGrZTvkAt7QEgtQ/7wzjLHpE68Vi04Y=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\rl8avrva5j-{0}-3djr1lshgb-3djr1lshgb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq#[.{fingerprint=3djr1lshgb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "smp0gt56xk", "Integrity": "PArPaNRR05jB+JDVm7ielqd774A1A1POge6nxh/VMTU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 55271, "LastWriteTime": "2025-08-13T06:09:24.6015772+00:00"}, "vsU17Uqg47X7Sc0HPv3Lt9SyOBxROS+Rl3g7y62pwzI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ez03ttwnqe-{0}-ub9sra6ubv-ub9sra6ubv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory#[.{fingerprint=ub9sra6ubv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d6altbm6d5", "Integrity": "xU8tNgjVOF9bmHJRcuU6IDJbVwNjR8oat5Ybd3qY7G0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 20547, "LastWriteTime": "2025-08-13T06:09:24.6035778+00:00"}, "hTiOlyoa8MVz8dUPM/atRdaQAab0RJpP4Awy4tX/WLU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\jq2h3qwo4i-{0}-346n69ja1w-346n69ja1w.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json#[.{fingerprint=346n69ja1w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i5psbi7mvj", "Integrity": "VtG7QnIonM8hzBGyByHk2HTZemu0OmnOShvdsgpj9Zw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19490, "LastWriteTime": "2025-08-13T06:09:24.568+00:00"}, "Lcb3SBH5Mrt6fgOtYmkUZOdZFiDSFYY0g28xrZwWW3Y=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\45is43ruwj-{0}-eupgag7vx5-eupgag7vx5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http#[.{fingerprint=eupgag7vx5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "loiqkrmtna", "Integrity": "654FulOg9qnc9OSAZP/125wGc0m4sfwcxNctStAKx7I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 113536, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "57vOeCTXzS7E0d9dhOykmozgk8Dd01mPJNsLhyDWqFA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\pcwospe1lp-{0}-m0tberhw26-m0tberhw26.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener#[.{fingerprint=m0tberhw26}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ja364f2x5e", "Integrity": "1Snk6wdtqtHGmagUcXRhpKWIFVkqiOR48TvtrClqWBc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 16009, "LastWriteTime": "2025-08-13T06:09:24.5925543+00:00"}, "gWMTMrKSkH2oCb3FEQKzbHa5nfpgkzo0ZwapTKuWEzM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\7ti5h58rif-{0}-7wmkfq1voo-7wmkfq1voo.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail#[.{fingerprint=7wmkfq1voo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wx1bfs34zk", "Integrity": "bJLUuUtzJSjaXJGnRL5u/FEVIQ6EWDxxF6kdv81AwvY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 41477, "LastWriteTime": "2025-08-13T06:09:24.6005778+00:00"}, "pPeHTzMAuUVXyL1z9Uyf80Q+UG4giwfbAgEsUZML7N0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\82r1g4we6k-{0}-ee8vwc4vcc-ee8vwc4vcc.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution#[.{fingerprint=ee8vwc4vcc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4vetenh963", "Integrity": "gMpSf1orS+hph1sOgVN1UaGGYrgX/WFEoUoph9UGvpw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5907, "LastWriteTime": "2025-08-13T06:09:24.6015772+00:00"}, "5VNg6xFUC1IViQ+qtfIb7MJeJ8fRQKFOxJW+x6RfHJs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\em99c8jnhw-{0}-h1hduhi84u-h1hduhi84u.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation#[.{fingerprint=h1hduhi84u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sbp02d7as4", "Integrity": "OAby/HFG9vc+4B7CtyQBuX7mAy0n8ztmsIZ/Be8yvjs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 12686, "LastWriteTime": "2025-08-13T06:09:24.6025774+00:00"}, "Xft4rhJgKojdZt7nHglMpxmu5XwfZQJcJVzj+D/Yolw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\h34ni7e34g-{0}-y4g427qvfa-y4g427qvfa.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping#[.{fingerprint=y4g427qvfa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8seju6crsk", "Integrity": "mAewmeGNfUBRukBCYI/914Ci967H5xFJo/Zly4e/KXs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 7503, "LastWriteTime": "2025-08-13T06:09:24.5669951+00:00"}, "XZkM7aVAv8PNkEAri8ZreC11Z7AHsv4ssRFj3z05CzE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\94cb79qhd1-{0}-zv1ut64ban-zv1ut64ban.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives#[.{fingerprint=zv1ut64ban}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hqe6mxtelq", "Integrity": "GdpA7250jskYLr90zEBdYe5p2ACS9FXhU4oQUq3K/xk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 45391, "LastWriteTime": "2025-08-13T06:09:24.5760148+00:00"}, "w0aPjdAE+jrf0tJ0FZBjxYFlLws+hHQ8F8wfsyZf4/o=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vuxv0oh5pv-{0}-lnozeoe9re-lnozeoe9re.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic#[.{fingerprint=lnozeoe9re}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "58zycxkdqr", "Integrity": "1Rb4jsnZt9p5oM2RJlQJPC1jbMUWA/+tNe7rP5TW2u4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 10836, "LastWriteTime": "2025-08-13T06:09:24.5820231+00:00"}, "LF6vjfZZNRLTo035LvSgNOLwiL6N6lASxXgX9aCNVug=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\gzsmo1yn5q-{0}-omoxxcqo90-omoxxcqo90.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests#[.{fingerprint=omoxxcqo90}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cqtdci0h40", "Integrity": "xCuzhGRvCzETq68XqAvUbtr+Im9pED0sok0JTeNXHBo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 20253, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "7VkZYcmTIBHJM3NOdj1WOdpL725N8MJfcK/2VlZLIJM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\m932zgjobt-{0}-t3a07csu2b-t3a07csu2b.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security#[.{fingerprint=t3a07csu2b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uqon2mzc8o", "Integrity": "+pjvw3ruCLqHXxCz1zithFOPOPEePFmogvfEk1THHFA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 32524, "LastWriteTime": "2025-08-13T06:09:24.593555+00:00"}, "Ul5M1VZ53R8qt5W30gj9ufGrSlLj5U17MI6BU01SGbE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\rq1mtysn1k-{0}-5v95sh5c67-5v95sh5c67.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint#[.{fingerprint=5v95sh5c67}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zlbir1205i", "Integrity": "R8rmpIgNQchP1NMQYAI1rZAoiFpxgxRiqnGXP4+ydwk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 2152, "LastWriteTime": "2025-08-13T06:09:24.595555+00:00"}, "neONv9XEkycPq51CLxWDI4pcqpwIGOgE/jtNtNeA9Nk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\s1efqdiea3-{0}-ww3h8yu74p-ww3h8yu74p.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets#[.{fingerprint=ww3h8yu74p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ot9m8bjhum", "Integrity": "PF+o1MbsC+93PkrqPHe8/XKIACXpWHwlDryWZjT3jsk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 22953, "LastWriteTime": "2025-08-13T06:09:24.568503+00:00"}, "IcM1ia4UyU9AetzNnRBWS9vVcfQMLHfIdvKutBhh1tg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\q2jua0tmuz-{0}-345793p9fr-345793p9fr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient#[.{fingerprint=345793p9fr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hqe4lkcfku", "Integrity": "Z1dHxianR7H+OMYXJtqS6JKSvvRghsuuGO0C29ypfoU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 14319, "LastWriteTime": "2025-08-13T06:09:24.5750148+00:00"}, "y2W8odyqF4wvIUZ/1fTOJjW1XsUXVN8YOkzZZbWz5PM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\8kbh7hx785-{0}-odv41wuu54-odv41wuu54.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection#[.{fingerprint=odv41wuu54}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o91o3hwzqk", "Integrity": "lFy+BAfPRtfWFLzJ9YnD1KPq2NpTIgk/ppi38REPvCY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10167, "LastWriteTime": "2025-08-13T06:09:24.5800229+00:00"}, "ISlvXF4QOWVUb+ZfRxd+9Qo5jTbPQ6hIgpZi5T2qaUg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\gbcomxyi1p-{0}-ksx7w94zni-ksx7w94zni.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy#[.{fingerprint=ksx7w94zni}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z5q97d6g3f", "Integrity": "SfhttF5640XI6GYrcDGUckL/YdKb6HoyzxzBHCg0ttU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5575, "LastWriteTime": "2025-08-13T06:09:24.5870262+00:00"}, "F33y69lT9yDNqPdoq1vmdYrFKmwRgPhRHsZ8HHF7cIc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\9jdih4wfh0-{0}-b37svw0y4i-b37svw0y4i.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client#[.{fingerprint=b37svw0y4i}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9czklybx9l", "Integrity": "FyN0F7rfGC/asE4fD25Bdm+Sq5xVFGyn9tSXhLsPpZA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 16984, "LastWriteTime": "2025-08-13T06:09:24.590555+00:00"}, "PjjEDgd/kxahszRJrEujGtmj02s8NHDN7mLVDijbgh4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\42zck7wz4x-{0}-9fasahbeiq-9fasahbeiq.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets#[.{fingerprint=9fasahbeiq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8rwwylp5qs", "Integrity": "1BMYx1Cw1qIyTsF3vaqVDqHo2EjPclyPY5AZyv7ScI8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 38019, "LastWriteTime": "2025-08-13T06:09:24.5995777+00:00"}, "Ecrske+wQUrGfi4X6XeoQeuanmIe3dB6NOnZ7rgiBC8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\483dhelpts-{0}-qt5fpja9tg-qt5fpja9tg.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net#[.{fingerprint=qt5fpja9tg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rqifglrb09", "Integrity": "owAcbjtoQgtT/So4jP87HqATQ9GkhKjCkXwlbYRGA/w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2730, "LastWriteTime": "2025-08-13T06:09:24.5669951+00:00"}, "UkFSrHgSbpYH1u/b3rNk7VB04hApP+JoKT37n69Ujzc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\tvgd981dy3-{0}-i6kirq3og4-i6kirq3og4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors#[.{fingerprint=i6kirq3og4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sa1vauehol", "Integrity": "rEVZXdbH5DuhWWmnAuB56nM6edF9/xRBi9ZYjxG3vwI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2237, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "qtdLU56u0DdQMKQ3oxInbC3Dtt+oXLyNy1SAd4pG5Gc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\hfdqawm983-{0}-497r8m9pev-497r8m9pev.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics#[.{fingerprint=497r8m9pev}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j8p4o012tj", "Integrity": "wwBPncrNI7MX8kHYaAxMcMZx7ik6e15/4HcAsEtqJaU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2015, "LastWriteTime": "2025-08-13T06:09:24.5770148+00:00"}, "SoiZ1a0ET3H0dahnRSDhZhLJzietwvfiIEvRstt9Jmg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vm1gcbf22l-{0}-k9az0iuxjb-k9az0iuxjb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel#[.{fingerprint=k9az0iuxjb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "knujs8pz4y", "Integrity": "8C0YKRnpnnVzih0m2lkzD1kmA7ZHNxDCz/tCHhwLoj4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 13315, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "SMJzAlH6iQTE8o6WQ41kg+Ra2Upz2apukwcgq/Fga8s=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\8f35vmpev5-{0}-mnc7tnpegn-mnc7tnpegn.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization#[.{fingerprint=mnc7tnpegn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4nhqyqjd0m", "Integrity": "0FUeOiTPhuwSVz9F3IN29CKXYxDLljnzWVaYWVg1S9k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 299896, "LastWriteTime": "2025-08-13T06:09:24.6232345+00:00"}, "+QAYpH4l+8Huo2nNoero+WJF0Xhp45+OgvHYXd3Mx8A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\i9sh5r0msv-{0}-58q5onb7r6-58q5onb7r6.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri#[.{fingerprint=58q5onb7r6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pchy46s1xc", "Integrity": "pxdcYQ1DLrLK/h00+LJZMhKB7+EFevsfZTtwDTxXck8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 41377, "LastWriteTime": "2025-08-13T06:09:24.6277445+00:00"}, "+TL/CnegQjyr5hZGmpmZJAUy/xoj48ecaKnT5snMoFI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\9xu5xe2r9n-{0}-35ud51k85s-35ud51k85s.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq#[.{fingerprint=35ud51k85s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m1rlsy4m60", "Integrity": "KqCyxuZYmsAfwEPPZ0F/488iJkuvZzfOu2eVW/qCExI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 58363, "LastWriteTime": "2025-08-13T06:09:24.5750148+00:00"}, "cNjoVxedBiTfk/MjW48Pa6sbfSCAx6zzvzLwp/cAXU0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\8xsqevfdzc-{0}-ygkocwikl4-ygkocwikl4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml#[.{fingerprint=ygkocwikl4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvh4j95sn3", "Integrity": "ecUo9oQUoTPdJcPGncPE0msrwGPXxbZkBz5sr3VppjM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1051048, "LastWriteTime": "2025-08-13T06:09:24.6942582+00:00"}, "M5YAAwn7qTQN72Ps6fqvWZtU/TLs9O+/cIuD+SdQhnM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\sqkutjk3c4-{0}-13d6e679le-13d6e679le.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy#[.{fingerprint=13d6e679le}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o8hjpogzto", "Integrity": "+EjhOpngMTzKRlzuKNVyDMvnxjw059cVF0ukmolE9ds=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 12761, "LastWriteTime": "2025-08-13T06:09:24.6952581+00:00"}, "9Oj6CdE+OHrY1MUZKThnTaYNsMunILzG8Oir6e+Tssc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\54cvmzu53d-{0}-tnlqh325q4-tnlqh325q4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration#[.{fingerprint=tnlqh325q4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hho693depw", "Integrity": "i1ngFtiXTy/6FsOQm7c4Azd4zmVw6j3rnEhMf9VJWlE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2252, "LastWriteTime": "2025-08-13T06:09:24.595555+00:00"}, "oAABOkUPZuwhKpJ471pGWrRl7EqziIKcfcd/e6Aud80=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\n1kq19s6el-{0}-6pezgz31ve-6pezgz31ve.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight#[.{fingerprint=6pezgz31ve}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fhwej7xl0t", "Integrity": "81qMId0YlE5nbgbSAnq/FkOfMGfzoPDALAg+ttPM93Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2211, "LastWriteTime": "2025-08-13T06:09:24.5985773+00:00"}, "MML3XhVMlbRy6loQ9VujYlJmTdIX1Jkvka5WFcpGJfs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\x5hqg6cowv-{0}-1sfjh9emmw-1sfjh9emmw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit#[.{fingerprint=1sfjh9emmw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tiuqkhq61b", "Integrity": "BbVCIMEgnUdXbOg6jwDW1igLbg2QSg+M/3QZ0Uz5uMA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 51910, "LastWriteTime": "2025-08-13T06:09:24.6045781+00:00"}, "o689Xr8G5luALleyPPHodkve3Yxqtj8Jnw8FzqD4BCc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\286qe4bhv1-{0}-o3fapkxyot-o3fapkxyot.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions#[.{fingerprint=o3fapkxyot}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gl3p1r7887", "Integrity": "jMQ10h6dzoeIGnw221ifset/iHvrVSgm6QOMSAiXi98=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2133, "LastWriteTime": "2025-08-13T06:09:24.5669951+00:00"}, "MI0oAPpywCTd/3kQ9CAYMTxUW22FAX3odxHiyAMlB0w=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\dj0n193gf6-{0}-tx83z6ho7l-tx83z6ho7l.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata#[.{fingerprint=tx83z6ho7l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t3lloxkty0", "Integrity": "5+UdfXW/wvKBdO6HMMV5JoZQoNxsgO1Je2NvHHUhIsM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 191567, "LastWriteTime": "2025-08-13T06:09:24.6015772+00:00"}, "YVNjDM421JXLN2PIQRzDBmcaIuXe/Jn+7QZ1CU9s7+s=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ul1bfq645u-{0}-z6035msxdy-z6035msxdy.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives#[.{fingerprint=z6035msxdy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lakktre51v", "Integrity": "vUzs6DTCtKI+1YzTCKFeig232Mfbf0AC6HDILVBd2+M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2339, "LastWriteTime": "2025-08-13T06:09:24.6025774+00:00"}, "+82D0FjQzGhlYzFj9gxgwTfFoOu5z7dDpf7mHmPhXhs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\1a6zcvk1ie-{0}-xqbpbwu9vz-xqbpbwu9vz.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions#[.{fingerprint=xqbpbwu9vz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uxz5g5z1gt", "Integrity": "zgYrUuxxIrPjU/BCoRUzQPQNpmywvWT1PC49els68wM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5636, "LastWriteTime": "2025-08-13T06:09:24.5975735+00:00"}, "pAOuEtGqF241BG6JHK3vzLPDMo4Oyul6smiR6smO3f0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\bpd6squtnx-{0}-1kaq8volf4-1kaq8volf4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection#[.{fingerprint=1kaq8volf4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mb22iyztzc", "Integrity": "QgJIiiQjbh9pNGxH0rlsEMMMlJ2ZLd7O0oI2q87W+B4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2433, "LastWriteTime": "2025-08-13T06:09:24.5985773+00:00"}, "CgXVLhXPIQ0lcUvazZYI4SQdBPCsOvfRUh2k+PAjUlA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\tgagesl3sz-{0}-3d1gwadcaj-3d1gwadcaj.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader#[.{fingerprint=3d1gwadcaj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t0azmyai34", "Integrity": "gyTPBHOBz37W84V7sK3jBsrkbMY5B77huOuF7JK5hbg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2102, "LastWriteTime": "2025-08-13T06:09:24.5995777+00:00"}, "TMs5yMB8xRPbdmyy3AU3fT6KyJqEYdGRhNFF4fE8WKw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\c01xkjda8r-{0}-pdb0cwov9g-pdb0cwov9g.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager#[.{fingerprint=pdb0cwov9g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n7c67ifhnp", "Integrity": "U6KWPy84NlRffTpZ4zs+bxMvPXZZOowqqPmyJ72Cesc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2217, "LastWriteTime": "2025-08-13T06:09:24.568+00:00"}, "9PkaqWzaGQNDTO4+UR5E6ipgQ9pv7aokPuuGmdcm9aY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\awv4v28y8r-{0}-wfwt17t25p-wfwt17t25p.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer#[.{fingerprint=wfwt17t25p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kklb1i9vmg", "Integrity": "9LqoDHjlXgkCI2Zq7oQMY2qp642ZM7kLJPm4WUM9IfE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7574, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "74sezL+OOUNakB08wz7knY7q76wwPaPfF/OeEBml6Ik=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\zbujze6hyu-{0}-rt5a291rko-rt5a291rko.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe#[.{fingerprint=rt5a291rko}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "83lvqkk2o6", "Integrity": "fJngxmFEJbrxzg0XB1CAG9KgieIYn1l1dZJkQQAyuNQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2103, "LastWriteTime": "2025-08-13T06:09:24.5780199+00:00"}, "eQsObyfCdf9eFgyS2DcRSnHYPhnEt1251tWDfRX9b3g=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\b8i6749yez-{0}-gigtt0ldg1-gigtt0ldg1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC#[.{fingerprint=gigtt0ldg1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8rbkdoj5f0", "Integrity": "hHUvv/yqwa5zSgVWC3cNgaie0IBPHVx2araCk90A8qE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3019, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "AL3W4E5nlVfOs3HB4KZjrojsAMfmQFw2JtI2xrAJcOc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\jrpmfwb7su-{0}-kaw15hufc0-kaw15hufc0.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions#[.{fingerprint=kaw15hufc0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klk5o6a1ur", "Integrity": "eDg28Sc9ZdbZDpt3k13W5g6ETjTaQHGqgM6bHTwZ6Gg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2971, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "eMfBLDtkvjss0MyjNz0pYmKkPslblY0DCfjsQWP3sgo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\6pm647xnn2-{0}-7qypx0bvu1-7qypx0bvu1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles#[.{fingerprint=7qypx0bvu1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vu1p97a7v1", "Integrity": "p1oQhS813ZIWjDH8uFzEjV55o63p2wvw00GIMmKTxU4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2182, "LastWriteTime": "2025-08-13T06:09:24.5915545+00:00"}, "jq2G+uRXbzvzQ2HbaEwuPEEFD5m98LIu0pC5zQsAvjE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\631h038nxp-{0}-k67jm10rbw-k67jm10rbw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript#[.{fingerprint=k67jm10rbw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s4n923tqul", "Integrity": "pKSME6ZoRXjXZblt9MxuBLkUJOsDlq5idXAhI6Z0NGo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 30935, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "XJxlCyZ+UJVNnRreBh+Vq9ysgZBK5UxXWJIax1MyZgI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\b0jqemwfae-{0}-uanr5ywdiz-uanr5ywdiz.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation#[.{fingerprint=uanr5ywdiz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r4neqh2u3f", "Integrity": "ZbW9kNHYjqPkDJBZb929/PuDamKE+/zpbaQD+TjgK6Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2131, "LastWriteTime": "2025-08-13T06:09:24.5780199+00:00"}, "XnY9aeA5kbD7FdIBwi2QPEblQXrfIj8Kt7lzazyL4lk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\iitqlnnjc8-{0}-fel5k50x7l-fel5k50x7l.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices#[.{fingerprint=fel5k50x7l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hlvjbk6khf", "Integrity": "tUGxnRqoGP9cIMCN+B7hKduRAJTUUociWZmUFWzGbto=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 23214, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "vObNlHFwZ4UeduqBZo64pMJGx7ZSRMZKAg9BC3kuJ7U=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\6n6ju6tqrx-{0}-eoagj84dsy-eoagj84dsy.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics#[.{fingerprint=eoagj84dsy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtwgb7d5hg", "Integrity": "2X4j4fLfnOXsqoISopLVaqIV8lf4oCc8PFeEF4BeMoo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2708, "LastWriteTime": "2025-08-13T06:09:24.5915545+00:00"}, "JLFkcRxROFd7p+Utk2CTvYpdVSB7Q0a63yO+DCHnnUo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\iy0tymru1v-{0}-7g62ykjls0-7g62ykjls0.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader#[.{fingerprint=7g62ykjls0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8a85g5gtko", "Integrity": "0MIqzbe+pffyM0T/lZsrZnFLNPwP6ncIchPngPmu5h0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2292, "LastWriteTime": "2025-08-13T06:09:24.595555+00:00"}, "MGKgFm7c94/vlRSzJwbLvsl3Z7o8upaf2P4dIa8vUCY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\la0xlwigkz-{0}-tp0shtj6gv-tp0shtj6gv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics#[.{fingerprint=tp0shtj6gv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "86rfwy62na", "Integrity": "UP8OYsKxiTMEBjZxOl2KuoAJyXVs0lZyjkMf7piGVSM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 52000, "LastWriteTime": "2025-08-13T06:09:24.6025774+00:00"}, "ox4zWkh/xQt2QwQVpMNzEuIehk0KIptz/e3j1kqqZiY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ki6vvhlx4j-{0}-nvsnsgm1il-nvsnsgm1il.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters#[.{fingerprint=nvsnsgm1il}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i26ba3epvz", "Integrity": "hrYFHqG6RM2GNpIQPTLhcpQ0hmtC7z8Z1iByziM2F6w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 24079, "LastWriteTime": "2025-08-13T06:09:24.5715069+00:00"}, "dfayz5NAHzbHyPTnm+5e8LQN+paad5meQtgR2pNnc3Y=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\zx7pp6g7y1-{0}-4t62p34f9u-4t62p34f9u.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json#[.{fingerprint=4t62p34f9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i3v6wah9o5", "Integrity": "7ro/CctMgQB0qz3PdOdURIXFFcv3gIQYlvUaPoMSwys=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2231, "LastWriteTime": "2025-08-13T06:09:24.5760148+00:00"}, "0QtGSPwC6WnSzP1PfO1/nCMP4a3ozlTjmEq233r+TuY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\s67dldjufq-{0}-8mh3k1xubv-8mh3k1xubv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives#[.{fingerprint=8mh3k1xubv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3phssebu4p", "Integrity": "lwzwlXEfuqFYokvJkKfqc0oSaa4eT3CJOd8cVZTJXBE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5393, "LastWriteTime": "2025-08-13T06:09:24.5810236+00:00"}, "3+0CaH2D90qcS44nywzUpTQaO0OElevr0lbDyk1s9xw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\tdir3phpww-{0}-6heyz9oosd-6heyz9oosd.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml#[.{fingerprint=6heyz9oosd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ojxklpg90s", "Integrity": "LKB7cp1tD2LtqoKC3d7RPLWdg59qQkfm/+eoamoWvHE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2533, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "SSAcmw2M1HfciwRLiAeBkD/FqF3X8xmMrvCFRIwgkf8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\z91tpfw4zd-{0}-1oa8jl3amd-1oa8jl3amd.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization#[.{fingerprint=1oa8jl3amd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q7q6cnhefb", "Integrity": "NqIv1N5ehS9mkQGrbjlOW1P1KPr6WiLhY6I+2z6RA/c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2479, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "bsiymTUYY4hCqxHYlSj9PBRBiaI6FY68ktyr9HjKfZ8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\cdu8x65bqo-{0}-xqvdvko8po-xqvdvko8po.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime#[.{fingerprint=xqvdvko8po}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "670w9y6opa", "Integrity": "/mV3vaq0SNGKFQl+G1sBgkmd1OdWYDvbWU/Z+UoFEg0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10642, "LastWriteTime": "2025-08-13T06:09:24.5925543+00:00"}, "bwqJ7EKTbnzU19lGrtIeJbPQiLWGWO/aCjbSELPm+xM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\lb1qq5ox57-{0}-jtnq7vre8d-jtnq7vre8d.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl#[.{fingerprint=jtnq7vre8d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nretya36yy", "Integrity": "A2rzxseDiIw0JQ2IYnmlTQ0VXfn1Q+aaWF9hSATBqp8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 16798, "LastWriteTime": "2025-08-13T06:09:24.5715069+00:00"}, "R0cntRES26Y8TOz+xuOXBeXVAqx9wDm+zk9oxuGnFx8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vvxsqfzb9f-{0}-9fyr8onzdl-9fyr8onzdl.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims#[.{fingerprint=9fyr8onzdl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "28lhf1e0wt", "Integrity": "ToexBqV61zf/Bvghud+1p23vPF6oWCRmL8cYO5jLMq8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 16078, "LastWriteTime": "2025-08-13T06:09:24.5770148+00:00"}, "vwRPe0qNuSi/rJhj5PO17Jpy4d8RBVkKifD8Hgp8IeE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\epvgw622kn-{0}-49z3p61zui-49z3p61zui.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms#[.{fingerprint=49z3p61zui}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nsly3ywhy3", "Integrity": "KXL+elsqJ/rMhBV07D/vkYs34LDvwBMKdwgwYrflmDo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2679, "LastWriteTime": "2025-08-13T06:09:24.5820231+00:00"}, "25b56zd9dXSM1ALq7koHxRsmvS8QXBu5FddPJcMB054=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\gnnffos883-{0}-01efu89mjc-01efu89mjc.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng#[.{fingerprint=01efu89mjc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "es3wc4kn39", "Integrity": "Lvcg6dyFdL8FkgAVLmjm5r0wHrK/y8JCgENNK12j/kg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2446, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "7EX61C/6zA8O/K5yInLMcxrXrM1DolIIrJ2FOZrBFrI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\kcsr0jlkzs-{0}-m6kt5rkphi-m6kt5rkphi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp#[.{fingerprint=m6kt5rkphi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iryetbad6q", "Integrity": "ftDk4F0mlVkVCqbVVWHLF0rn3aq3ZST1Vq/9fFwPRfY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2311, "LastWriteTime": "2025-08-13T06:09:24.5895502+00:00"}, "Bf4fvAp+n//rRtoM34Mhw/05R716Zmde10Yng+DiHg0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ykbxr1r7y8-{0}-4j2304etti-4j2304etti.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding#[.{fingerprint=4j2304etti}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xaplqpxjn9", "Integrity": "p17RN1tUF2uAAKF99fklrvIHVFcHkYN/DdVRaXY2ges=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2254, "LastWriteTime": "2025-08-13T06:09:24.5925543+00:00"}, "uUYFnDPPi5pjARmUpSkC3K8c6zyLGK7Mr0rezXfHRes=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\t169uoi1u2-{0}-rgf4gnhaju-rgf4gnhaju.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl#[.{fingerprint=rgf4gnhaju}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "orhizatwft", "Integrity": "jJqnGUhBv0KWzW2hVUCb/MHliXR/2tLmi9i28xOK4ww=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2184, "LastWriteTime": "2025-08-13T06:09:24.568503+00:00"}, "wN5uO00N8bZutUcp6SygWA8gOil3mPo0T1GhGBeLn+M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\8yojc0cb84-{0}-wqi94vu5m0-wqi94vu5m0.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives#[.{fingerprint=wqi94vu5m0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c1hfgw6cu2", "Integrity": "APQkRu5wL3dD+uSkEjQgytiFJM5RsFaVyYJ8j4rYeHg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2309, "LastWriteTime": "2025-08-13T06:09:24.5740159+00:00"}, "wnjre3mjVnP23F4TqXuoEB7tlmpSwYK7dJYmN7QIO40=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\7xj4vtlbe8-{0}-e4s9csihna-e4s9csihna.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates#[.{fingerprint=e4s9csihna}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z323cdc7wr", "Integrity": "b40EE+RBEVB47m6I6ylNQkmVsTRRWbXE9vRXqc4M7dw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2645, "LastWriteTime": "2025-08-13T06:09:24.579023+00:00"}, "3Fzp8/OP5R5hCEIhFjRFZgELpN8Uqa1zubdUz2mqRXI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\9knoi3h2b5-{0}-z9o6jihhaw-z9o6jihhaw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography#[.{fingerprint=z9o6jihhaw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h7m0rhrjps", "Integrity": "bb9nKlZtFF1BWonFQCUHfrx8njTrkquPMbL2r3r5cEk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 190176, "LastWriteTime": "2025-08-13T06:09:24.6060805+00:00"}, "1sMmVb2pYSkZeKNTwdLPU9jp885XJ6GO6fNEMofmhu8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\3qubpab9b4-{0}-d93pggsupp-d93pggsupp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows#[.{fingerprint=d93pggsupp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zcteqrh34n", "Integrity": "IwJ/rRn6GhYp8cazSuPJhJQfC+CA92Ay3l5qV+eyzDY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 11068, "LastWriteTime": "2025-08-13T06:09:24.6080898+00:00"}, "/iBtzG/M4B17SAhmnqbMN+z+JRCSVaXCTSc7JeI4ZJs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\bv78doa1nt-{0}-sa193kq3m2-sa193kq3m2.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal#[.{fingerprint=sa193kq3m2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "etxu6a8wiu", "Integrity": "srTk/Q4MDmQcMh6I7jQxA7rBiiSj+z/mToT4O/mmy3Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2137, "LastWriteTime": "2025-08-13T06:09:24.6080898+00:00"}, "C6QyqIHyQcPxJ6PRhQ7DDidaiB0kIrsopPD4Pn1qmxE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\rxhnrlxubj-{0}-z4ma9duddm-z4ma9duddm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString#[.{fingerprint=z4ma9duddm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qwz0oxftca", "Integrity": "Nt5VraBobnvAewaGwAXfphktxnQGbSX61kYtd+gRMpQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2173, "LastWriteTime": "2025-08-13T06:09:24.568503+00:00"}, "HscNIYDYIfLq8GArfAsiLmsTIFdk16m8vA6D6fHjM4Y=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\y1ooyp9uc5-{0}-rztf0whns2-rztf0whns2.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security#[.{fingerprint=rztf0whns2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ma8x7rzffi", "Integrity": "wrSJEchRkmmwsxW89uMD/ePtANgxSFbnZuPYfcy2UTM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2945, "LastWriteTime": "2025-08-13T06:09:24.5730106+00:00"}, "iSCOkne3mNmbUPPXJf97oAdN2hupePTOshxSRaIPVxs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\gdk781dj5t-{0}-bnlcmxi1w6-bnlcmxi1w6.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web#[.{fingerprint=bnlcmxi1w6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wbc09gg9r6", "Integrity": "bGfFZWSQ351UFOD8B8Qph1RG4uVhX9sy5zyIS8Qstgo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2519, "LastWriteTime": "2025-08-13T06:09:24.5780199+00:00"}, "HnM9tJ6eJ6MhodClVqZbi/E7BMYSy7CrgxXflCQzSJw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\0ae51qwp0n-{0}-drbdk7bquo-drbdk7bquo.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess#[.{fingerprint=drbdk7bquo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h2vyoxnv05", "Integrity": "yT1Xhwlsf0oKRFTBfT8gm50+UA4LmE2mxq7AsjD1Gwc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2283, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "Kf67kLG5e9hR/9VJ9XCbTlHTfWyLEEcXQyLXsjA011g=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vw35pjcn0b-{0}-7iff0d2lb6-7iff0d2lb6.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages#[.{fingerprint=7iff0d2lb6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7s<PERSON>uw<PERSON><PERSON>", "Integrity": "meWuhZKGhfH6y4UV1Z6f3IRXfOHFODg28ZnKjn9ouTw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 514863, "LastWriteTime": "2025-08-13T06:09:24.6107233+00:00"}, "IqwNINKjmzlz3gM+X1Y7w5lpSIEvFZZ3068fqN4kGQU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\x9outkujcw-{0}-dc711vstge-dc711vstge.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions#[.{fingerprint=dc711vstge}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9k9vmojgla", "Integrity": "Kah4fRKsKAd0wXhjBVdT4jmRAn0XpBk2nctc4sDI16I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2231, "LastWriteTime": "2025-08-13T06:09:24.6117232+00:00"}, "71Sg30Mge6fAAnI0xY7p7QHZ0Pr66Ah6OvVz8f5+mwU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\j52rvyj0yw-{0}-3696nx7xrc-3696nx7xrc.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding#[.{fingerprint=3696nx7xrc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xdp6p2epv1", "Integrity": "Bec0wDFpEZVn09cIQ3AuMN11ktIfSjfv93kglrCx4gA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2217, "LastWriteTime": "2025-08-13T06:09:24.568503+00:00"}, "2ed3GaKsXy3a8RRnQ0fmJlAGqui/vZbWiIKZxllKZ0A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\3v2zcik8bs-{0}-ksemyzm5ld-ksemyzm5ld.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web#[.{fingerprint=ksemyzm5ld}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pikfidvm24", "Integrity": "oSthMNIhA5EaiZ8+8Qt+OOMRx8TR89oskgWyDut724U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23403, "LastWriteTime": "2025-08-13T06:09:24.5775175+00:00"}, "IoU7K0OsZgU5StwaUnsA/cMPiDeBfVp+RAgkh520HjE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\e4k2mjuyl2-{0}-x92ye0v3y1-x92ye0v3y1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json#[.{fingerprint=x92ye0v3y1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i0vtayb979", "Integrity": "vrvfxyAfAX3kRXZsNkhaNRVleyuooDPY47GH2USFUR4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 216853, "LastWriteTime": "2025-08-13T06:09:24.6127228+00:00"}, "urgjelVer/xTipyslvSreQZmpMJZg48D/SFY40d8qW8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vaf1psfujv-{0}-dnj9z23s0g-dnj9z23s0g.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions#[.{fingerprint=dnj9z23s0g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rkp1fd9cpw", "Integrity": "RXWosrLgF1na33iZUQZyGegZeRZZUKCHIgInWNZErQc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 153662, "LastWriteTime": "2025-08-13T06:09:24.6107233+00:00"}, "FGw+bzXVecF+YQP7jDDt6Pe2F2+CXowJHIOLtbje2vU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\mvd69cc4m6-{0}-tomvzoqfcf-tomvzoqfcf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels#[.{fingerprint=tomvzoqfcf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jzp23cb6oy", "Integrity": "D7NZRTviiZxz4BYPW3d05UykXtr6IS4eg/hYYydqDs8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 20565, "LastWriteTime": "2025-08-13T06:09:24.6127228+00:00"}, "hINl4Hfu7Shz/9ntIjLVUo3ekSf2NSNGb/i3FPYVRHM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\naswe3dl75-{0}-0g3k20op8c-0g3k20op8c.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped#[.{fingerprint=0g3k20op8c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xw4wsum4as", "Integrity": "H+tNtMOjpdCXKy6k1MsZuT5kX6kIved5yv6KJcQgptw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2275, "LastWriteTime": "2025-08-13T06:09:24.6127228+00:00"}, "ceio92aI9sya+htawprFBjJ6LFjH1bul5cbdqY5l4RI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\wn3z8nzxqv-{0}-55tewhp7kf-55tewhp7kf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow#[.{fingerprint=55tewhp7kf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "txf8gv8e56", "Integrity": "TxHJkkFeTafy8vSrzj0N5iyWfdVDfPc3q0x7bND/XvU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 73169, "LastWriteTime": "2025-08-13T06:09:24.5770148+00:00"}, "5WQ4quNJ230pz9VUv+ZTxop9Wo1EZOsPEkkJR7iuBhY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\5ep0p4n4f2-{0}-sce61xpslf-sce61xpslf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions#[.{fingerprint=sce61xpslf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4uu3byrvxy", "Integrity": "3OHfdFy1Jx8F3e/u5Q2euVV8IqTSrIYGoSSkaGxq1Tw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2276, "LastWriteTime": "2025-08-13T06:09:24.5810236+00:00"}, "tCcRgA4/VZ1ab2fzwSiqlJpeusb6eAUwweLFh7MXWPc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\mq3le2ogsr-{0}-548crbk151-548crbk151.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel#[.{fingerprint=548crbk151}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3yq7kjux9s", "Integrity": "1ZscPBr7BfbBVF6vdaGTGwc2Qyp4e1gmq38qSYqUbVk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21027, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "tmzxhL52A633NfW2+FwFliFcB44MamhM676KmxmdO3E=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\xlbzli35oc-{0}-x86n4j91or-x86n4j91or.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks#[.{fingerprint=x86n4j91or}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cgw3jmu428", "Integrity": "9E19Texq+VKY0AneWPPKfpQ4ivIt6jF40rgqxZw0Nkk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2544, "LastWriteTime": "2025-08-13T06:09:24.590555+00:00"}, "fKmf5gC/0RMI3rxE/+Fe1niLLZxg1PwHdBX4cU+0KHU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\5pyj82ayu9-{0}-07bttawl88-07bttawl88.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread#[.{fingerprint=07bttawl88}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "toxfy3um1p", "Integrity": "s/lKEXuBT/L1b4dNP5U1xTlb+2/3cIK/g70Qo8V1uls=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2310, "LastWriteTime": "2025-08-13T06:09:24.595555+00:00"}, "4xYJFZychfMWsJZ2uXQIIPjQtwKecJB1ccifyWh4Tm0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\p2ujx4rie4-{0}-zt447d1n6v-zt447d1n6v.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool#[.{fingerprint=zt447d1n6v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bwuihh3yjg", "Integrity": "FU5FB6WMQdfx5vUH+IYKAlrMPNh6UEC/nlvHjyoswZE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2225, "LastWriteTime": "2025-08-13T06:09:24.5975735+00:00"}, "iminsH5gVhRfPBxxBp5pu+rSN209s/1J1sJNCzSPcPI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\rqp8olh4yh-{0}-r3c1h58f9w-r3c1h58f9w.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer#[.{fingerprint=r3c1h58f9w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qkrg4ykxwk", "Integrity": "ZkTgB/SsBNHcnjMj37TOuqw6gUkKhOSpcvImLgBX53k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2107, "LastWriteTime": "2025-08-13T06:09:24.5715069+00:00"}, "kZthEBR7OSVd0VzjLx5tVbIz3p6Oe1zXNlCQ0qaSjbo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\3ozuy9kc4p-{0}-4z6mzh73ny-4z6mzh73ny.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading#[.{fingerprint=4z6mzh73ny}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0mx0dzvzkl", "Integrity": "2Aww/uGQzCjiT5/c3oSBO/6JdcHKsmnNT3LD3ts+Xzw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14647, "LastWriteTime": "2025-08-13T06:09:24.5770148+00:00"}, "0kmfXvazuQxPT2vKMhMwMLrqb2yGsGkLr0vZPDL+Rr0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\d0l56xbf1y-{0}-pil3cjgvw5-pil3cjgvw5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local#[.{fingerprint=pil3cjgvw5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tgyb8rhhdv", "Integrity": "w9hfpmhSy+CjV8OQLZg/GLKwP4HAES5zi5k0OGRk3XA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 51417, "LastWriteTime": "2025-08-13T06:09:24.590555+00:00"}, "oAXOfLPjgt0tw5GNu1j5mMOiyfrmpl7lhmLq/uI1cG8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\bj1ouvglsc-{0}-g6ni30uafv-g6ni30uafv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions#[.{fingerprint=g6ni30uafv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qx6q07q6ty", "Integrity": "nkF/Bfe7Uhcdxv7fbkb/COJ1vn+OYG2qaOUw5m8stWc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2355, "LastWriteTime": "2025-08-13T06:09:24.5945545+00:00"}, "9O8O3jz0dQmpoSf+DFSjGuXKwmkdKQViwmZoAL25fPM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ttrczax0uh-{0}-adv6hyw1vi-adv6hyw1vi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple#[.{fingerprint=adv6hyw1vi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cppza7n2y6", "Integrity": "IN2amQpSBFalP3cF5TcpkOD+wJXh4AMe+MyVcEJ7DpY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2150, "LastWriteTime": "2025-08-13T06:09:24.5975735+00:00"}, "TPtQ8nKH//uXHd3BTmLoOVrJh4EVAdWI3AHCCR0nrAA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\mgktwlr6fe-{0}-4yi0atwy17-4yi0atwy17.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility#[.{fingerprint=4yi0atwy17}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jhpxtwjltp", "Integrity": "udOxbi2cMNj3fkVWnbMyDZ3AB4RoMXWEBc2xr54GKTM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 9831, "LastWriteTime": "2025-08-13T06:09:24.5995777+00:00"}, "W+khrIOo5OYJxaRM4YQg4p/GPtU1+RHblUF6ynM/6Aw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\7s7ycd46az-{0}-8uickrr2w7-8uickrr2w7.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web#[.{fingerprint=8uickrr2w7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h2o3mijqak", "Integrity": "mapgm8dKEFFphk24qhSoI7CzYDGzFTTuNqEy2IKo45E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2098, "LastWriteTime": "2025-08-13T06:09:24.5725068+00:00"}, "Zg4Y5yic8ddGzXKJ+iuM8fmTYX3SSeTdbHpUFZEATHo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\lb5fihpb8l-{0}-idlgil0u1u-idlgil0u1u.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows#[.{fingerprint=idlgil0u1u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "91ccacz8t3", "Integrity": "7fFS1d6gaaEKkuiGXD5NCN2mux3YZWrd7Q6L/e0Wxlg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2260, "LastWriteTime": "2025-08-13T06:09:24.5760148+00:00"}, "DsZtE9g0gVipGrK227Y1JltxVcNPoDzRy8gv5uTMI84=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\roedkyhu2w-{0}-jhjtvo31q0-jhjtvo31q0.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq#[.{fingerprint=jhjtvo31q0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ivhc7pi274", "Integrity": "gg8npzWUagbqIvMul7uv3zis10lc0ThXft4PRQs1jSg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2189, "LastWriteTime": "2025-08-13T06:09:24.5810236+00:00"}, "iKY9GYmG9HFcFrSVpwC0Czf/l78YYWKreGqsgzdWI7k=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\tofgqw5pjx-{0}-pcqwh7wu97-pcqwh7wu97.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter#[.{fingerprint=pcqwh7wu97}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "naggvnx24o", "Integrity": "gX6T2rd4NFnDWNqXwG+Jx8PW7mAbVEM7OI6P9i4SYrc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 3982, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "udDjIJAJBuV5XJme4J42k3FtVA4QTEErip3i9XKAN3Y=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\o8sl438lvo-{0}-0x6beqi7zp-0x6beqi7zp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization#[.{fingerprint=0x6beqi7zp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lfd43nw62j", "Integrity": "/8NVRec/qhGnIxiW96iB1+zQ2KlPlJBwiDybckJ3AqI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2214, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "H2iWfm/jF8er7pCMyPfBguWfM+e/SonWdXeCOnWusVw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vwr477b0go-{0}-8luigpl137-8luigpl137.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument#[.{fingerprint=8luigpl137}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oy0lmn48yx", "Integrity": "IMprs0y7UdMstD+EhkTWCRpYuix5dYxaEj/hX9+YfUE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2370, "LastWriteTime": "2025-08-13T06:09:24.5925543+00:00"}, "MmXhfTYaNNjYAk6uZ/hqKpIAJbYt7m4AIoCzEk9Lyfk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\c0ybvnd0yx-{0}-rps120mzwr-rps120mzwr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument#[.{fingerprint=rps120mzwr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ttlupxhww", "Integrity": "K7zNZv+NDRhulrGmQl5MNiotH0KumjSmah9MI2Fso1g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2458, "LastWriteTime": "2025-08-13T06:09:24.5715069+00:00"}, "sq3lSZ1pLr5d5LA2ERJ8fjRRlWJkCAu5DifgVd+ESH4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\8m7dfebq8z-{0}-nj6o6nhskf-nj6o6nhskf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath#[.{fingerprint=nj6o6nhskf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5zddknzc6k", "Integrity": "//egFUKbX7X9AjOEL6G6FxxhFq39+qATP9NWaZr3aWc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2292, "LastWriteTime": "2025-08-13T06:09:24.5750148+00:00"}, "DDibMy1dY+VmxhPyVTgLWcmDbvtDTg9DbyoJAomafQo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ffbv6j6gp7-{0}-t7u25q5to4-t7u25q5to4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument#[.{fingerprint=t7u25q5to4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v8bdmckfxd", "Integrity": "0f9KXCThCFnmDON+jBqfx7qPYCh6698wR2snCrlBNhM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2331, "LastWriteTime": "2025-08-13T06:09:24.5800229+00:00"}, "8D41TCNyN2oLTyKy0UNR3J/1YtU7cDCrMFcgZv6Ygrg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\t2gwaq6x9s-{0}-ig2qir1wep-ig2qir1wep.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer#[.{fingerprint=ig2qir1wep}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lskn5gtgx5", "Integrity": "+wEXQ4BiPdOyNZxoBhw+wOChQvsFTysH9kmol19348U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2825, "LastWriteTime": "2025-08-13T06:09:24.5860238+00:00"}, "OAuy5BMw7G2z2Wp6vnjB08nWS6ryQwsM/oGgWJNabrc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vnx203s4w5-{0}-lnwczuoimm-lnwczuoimm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml#[.{fingerprint=lnwczuoimm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wlxcj8wjji", "Integrity": "JAjB3wgIGO98Cn//2MnZxyyVQTSSHdD6oM/FvrdbC08=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4200, "LastWriteTime": "2025-08-13T06:09:24.5880328+00:00"}, "iSkynvRT4rB3HOLM63KHAqtCAL1cq2Thb/HSxLlnXBs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\vbwjwnvk9f-{0}-00ls1afmp9-00ls1afmp9.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System#[.{fingerprint=00ls1afmp9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7sejobmv2f", "Integrity": "aeoSbukv6t/yOSgWEHA1igIaVkr0OHS+8UV7iqHy6Lw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11756, "LastWriteTime": "2025-08-13T06:09:24.5915545+00:00"}, "lCQeuLJApF+L/19q/uMgTh7IishGuHVWJFhwQCOJkqI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ywopjzzmnx-{0}-u25hol0de4-u25hol0de4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase#[.{fingerprint=u25hol0de4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iiv78q38j2", "Integrity": "SnsxrEkol2MLkwcoja+4dLRzJs8CoDaExyNVW3d1jSw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2503, "LastWriteTime": "2025-08-13T06:09:24.595555+00:00"}, "jt1Tf7z8ssy/CryC2MbyVPnvTU7sRZCsJ4x+lCwfPbc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ehrglyz6r7-{0}-brg9pkj3je-brg9pkj3je.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib#[.{fingerprint=brg9pkj3je}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pwp0f37f43", "Integrity": "1/EPTD6B5ASqxP1jEDD6n/Is3uhJjMz5xc+SRZDRAUo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14805, "LastWriteTime": "2025-08-13T06:09:24.5995777+00:00"}, "OO556Fu81EkdmHoOS0KoT76rPOLGkENudvNMegWaI9M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\b1a45mzccv-{0}-wuzd3f1y6v-wuzd3f1y6v.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard#[.{fingerprint=wuzd3f1y6v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2e01rr9wd8", "Integrity": "xnCvHe5T6PfmwOed9Q4j/PovOzQ3HruHJ1stXUUrnM4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 26065, "LastWriteTime": "2025-08-13T06:09:24.6025774+00:00"}, "Aq43r3fCX+HGmiKhMVm14HUE+WkEWkFrNo6P4VcTbrk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\92nwqxj4ji-{0}-05ksnw82w3-05ksnw82w3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib#[.{fingerprint=05ksnw82w3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vb2xp0sgje", "Integrity": "CwNtbQxPvM01XG/8EO2U4VO3mHIJ1m+OcGOx2CvDdY8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1496481, "LastWriteTime": "2025-08-13T06:09:24.7401132+00:00"}, "uK31aFsZc737cCBJOqVk/RNWwVwOOmu00zVODeTelBY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\atfbiuprug-{0}-vr46os3pyt-vr46os3pyt.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet#[.{fingerprint=vr46os3pyt}]?.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ff616hass", "Integrity": "bWMnF/Yt/Qzsdc2QEqO4wwUvxXMZILPR3YoKTBQUZSc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 12661, "LastWriteTime": "2025-08-13T06:09:24.7411134+00:00"}, "/XFgBmK5cPZQ82HqK9lzPaJ+HUx0wLzPPNGec2Q+01k=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\kqzxlj4q1l-{0}-es3ekshrlb-es3ekshrlb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js#[.{fingerprint=es3ekshrlb}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9l6pu8fhxf", "Integrity": "Y+Q2tpO1byf0qWtoU3Lz8ONySK2aGlH+1Fj5ioT/avY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 21067, "LastWriteTime": "2025-08-13T06:09:24.7441125+00:00"}, "CjVz8SRnIK8YeV/vNzsM3DzmzOqnhEdo041gAo+dnaQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\mx60r437t4-{0}-rtblh4npr3-rtblh4npr3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=rtblh4npr3}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ygpzgcwfny", "Integrity": "suuJwSBHkvFASeaEZwAJnUWiH9GvpsougJlVezmdaMo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 34748, "LastWriteTime": "2025-08-13T06:09:24.5995777+00:00"}, "3qthsJ0C9vv7JG/fyQS8YZRB4/Q8BVRim2jZpwWVcFo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\t451ifx54a-{0}-aqhezbunpl-aqhezbunpl.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=aqhezbunpl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v0k6y61mro", "Integrity": "IYvAT4mFHVPDqOh7OoBpNKyj2ml+C4Fzzn1TmEd8ce0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1179217, "LastWriteTime": "2025-08-13T06:09:24.7391089+00:00"}, "ag/EtDbRiRma4zgzVs4FtVK3Nu5uaH/OflMIUVqzCdQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ganheevyck-{0}-d1pzlaz2ez-d1pzlaz2ez.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime#[.{fingerprint=d1pzlaz2ez}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a9volc0z9v", "Integrity": "wqbMmW2BbDbOUBaVhW5c0+N9a24THChw4MuymiWOJ3A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 55607, "LastWriteTime": "2025-08-13T06:09:24.7441125+00:00"}, "QXZXE/27n0dKQ1nDhY0Ze5buPHgix3JxzhIRQ1AZoUw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ehd7i69oga-{0}-ctf2q9h8m2-ctf2q9h8m2.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js#[.{fingerprint=ctf2q9h8m2}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1samrp6awr", "Integrity": "70CNmEpfHJuxx1KcZjmtlM6EQDQFQ0UUsqhVZ1FiynM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 87809, "LastWriteTime": "2025-08-13T06:09:24.7546165+00:00"}, "1hgdOggo5vG86WrjOD/kec7cyBxQD/Ntw6BNtKDdops=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\ajonspo973-{0}-tjcz0u77k5-tjcz0u77k5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK#[.{fingerprint=tjcz0u77k5}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "su9h2nea1m", "Integrity": "JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 333110, "LastWriteTime": "2025-08-13T06:09:24.7822677+00:00"}, "A+UV0kKxh4N0GFieEdaWAp45TSIh+av6cr1ZW52iSVQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\yg5ej5faok-{0}-tptq2av103-tptq2av103.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS#[.{fingerprint=tptq2av103}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnxfkgr4e8", "Integrity": "G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 196037, "LastWriteTime": "2025-08-13T06:09:24.7978058+00:00"}, "v+pDD9ll9RmuV/ty2hKNx73mQU7fKqgsjLckGYondKQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\z0rgfvfjxg-{0}-lfu7j35m59-lfu7j35m59.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK#[.{fingerprint=lfu7j35m59}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v385ycndre", "Integrity": "S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 317618, "LastWriteTime": "2025-08-13T06:09:24.6288303+00:00"}, "Uehu+f0gszHUHQPB6zoI8svNTNOnkC0FIkxMpp2if+M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\utjp9azvus-{0}-x2suhlw9zl-x2suhlw9zl.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j0kossv2ur", "Integrity": "U3cCDWg4cfVbAqSXySzA0AwubzYsjYrPzoHb3LhYYsM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 13282, "LastWriteTime": "2025-08-16T04:15:56.426016+00:00"}, "LqIvvrxYaV3mgZFlNomtNlDhLMFGPSD/ELSk6XFkqOY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\l8lh1f8xrh-{0}-bq1w4dvjl8-bq1w4dvjl8.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ZeroKnow.EBS.WebApp.Client#[.{fingerprint=bq1w4dvjl8}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\ZeroKnow.EBS.WebApp.Client.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z30buceyzn", "Integrity": "T8Mu1zK4clJUSmnC7vjPu6V2rCV3eUVQ6B4W2Nnn7Dk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\ZeroKnow.EBS.WebApp.Client.pdb", "FileLength": 54272, "LastWriteTime": "2025-08-16T04:15:56.4310162+00:00"}, "qUuOBbP7cPVXabrrz4I9TnQQYPqSBXEA5dWd3Vx8Ws4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\nepntx6xic-{0}-8y2o4mw5nc-8y2o4mw5nc.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Release\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ZeroKnow.EBS.WebApp.Client#[.{fingerprint=8y2o4mw5nc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\ZeroKnow.EBS.WebApp.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "enwboio2w4", "Integrity": "9uWqEE8aY1egdzVLPNijZLfacB9LA+uuajKUI91hq2o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Release\\net9.0\\wwwroot\\_framework\\ZeroKnow.EBS.WebApp.Client.wasm", "FileLength": 36561, "LastWriteTime": "2025-08-16T04:15:56.4290159+00:00"}}, "CachedCopyCandidates": {}}