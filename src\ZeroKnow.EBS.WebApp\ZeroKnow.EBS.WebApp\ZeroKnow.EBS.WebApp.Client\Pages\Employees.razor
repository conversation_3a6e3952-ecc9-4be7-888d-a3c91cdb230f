@page "/employees"
@using ZeroKnow.EBS.WebApp.Client.Models
@using ZeroKnow.EBS.WebApp.Client.Services
@inject IEmployeeService EmployeeService
@inject NavigationManager Navigation
@rendermode InteractiveAuto

<PageTitle>Employees</PageTitle>

<FluentStack Orientation="Orientation.Vertical" Style="gap: 1rem;">
    <!-- Header -->
    <FluentStack Orientation="Orientation.Horizontal" Style="justify-content: space-between; align-items: center;">
        <h1>
            <FluentIcon Value="@(new Icons.Regular.Size24.People())" Style="margin-right: 0.5rem;" />
            Employees
        </h1>
        <FluentBadge Appearance="Appearance.Neutral">@filteredEmployees?.Count() employees</FluentBadge>
    </FluentStack>

    <!-- Search and Filter Controls -->
    <FluentCard Style="padding: 1rem;">
        <FluentStack Orientation="Orientation.Horizontal" Style="gap: 1rem; align-items: end;">
            <FluentStack Orientation="Orientation.Vertical" Style="flex: 1;">
                <FluentLabel>Search Employees</FluentLabel>
                <FluentTextField @bind-Value="searchTerm" 
                               @oninput="OnSearchChanged"
                               Placeholder="Search by name, position, department, or email..."
                               Style="width: 100%;">
                    <FluentIcon Value="@(new Icons.Regular.Size16.Search())" Slot="start" />
                </FluentTextField>
            </FluentStack>
            
            <FluentStack Orientation="Orientation.Vertical">
                <FluentLabel>Department</FluentLabel>
                <FluentSelect TOption="string" @bind-Value="selectedDepartment"
                            @onchange="OnDepartmentChanged"
                            Style="min-width: 200px;">
                    <FluentOption TOption="string" Value="">All Departments</FluentOption>
                    @foreach (var dept in departments)
                    {
                        <FluentOption TOption="string" Value="@dept">@dept</FluentOption>
                    }
                </FluentSelect>
            </FluentStack>
            
            <FluentStack Orientation="Orientation.Vertical">
                <FluentLabel>Status</FluentLabel>
                <FluentSelect TOption="string" @bind-Value="selectedStatus"
                            @onchange="OnStatusChanged"
                            Style="min-width: 150px;">
                    <FluentOption TOption="string" Value="">All Statuses</FluentOption>
                    @foreach (var status in Enum.GetValues<EmploymentStatus>())
                    {
                        <FluentOption TOption="string" Value="@status.ToString()">@status</FluentOption>
                    }
                </FluentSelect>
            </FluentStack>
            
            <FluentStack Orientation="Orientation.Vertical">
                <FluentLabel>Sort By</FluentLabel>
                <FluentSelect TOption="string" @bind-Value="sortBy"
                            @onchange="OnSortChanged"
                            Style="min-width: 150px;">
                    <FluentOption TOption="string" Value="name">Name</FluentOption>
                    <FluentOption TOption="string" Value="department">Department</FluentOption>
                    <FluentOption TOption="string" Value="position">Position</FluentOption>
                    <FluentOption TOption="string" Value="hiredate">Hire Date</FluentOption>
                </FluentSelect>
            </FluentStack>
            
            <FluentButton Appearance="Appearance.Outline" 
                        @onclick="ClearFilters"
                        Title="Clear all filters">
                <FluentIcon Value="@(new Icons.Regular.Size16.FilterDismiss())" />
            </FluentButton>
        </FluentStack>
    </FluentCard>

    <!-- Loading State -->
    @if (isLoading)
    {
        <FluentStack Orientation="Orientation.Horizontal" Style="justify-content: center; padding: 2rem;">
            <FluentProgressRing />
            <span>Loading employees...</span>
        </FluentStack>
    }
    else
    {
        <!-- Employee Cards Grid -->
        <div class="employee-grid">
            @foreach (var employee in paginatedEmployees)
            {
                <FluentCard class="employee-card">
                    <FluentStack Orientation="Orientation.Horizontal" Style="gap: 1rem; align-items: center;">
                        <!-- Employee Photo -->
                        <div class="employee-photo">
                            @if (!string.IsNullOrEmpty(employee.PhotoUrl))
                            {
                                <img src="@employee.PhotoUrl" alt="@employee.FullName" />
                            }
                            else
                            {
                                <FluentIcon Value="@(new Icons.Regular.Size48.Person())" />
                            }
                        </div>
                        
                        <!-- Employee Info -->
                        <FluentStack Orientation="Orientation.Vertical" Style="flex: 1; gap: 0.25rem;">
                            <FluentStack Orientation="Orientation.Horizontal" Style="justify-content: space-between; align-items: center;">
                                <h3 style="margin: 0; font-size: 1.1rem;">@employee.FullName</h3>
                                <FluentBadge Appearance="@GetStatusAppearance(employee.Status)">
                                    @employee.Status
                                </FluentBadge>
                            </FluentStack>
                            
                            <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                                <FluentIcon Value="@(new Icons.Regular.Size16.Briefcase())" />
                                <span style="font-weight: 500;">@employee.Position</span>
                            </FluentStack>
                            
                            <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                                <FluentIcon Value="@(new Icons.Regular.Size16.Building())" />
                                <span>@employee.Department</span>
                            </FluentStack>
                            
                            <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                                <FluentIcon Value="@(new Icons.Regular.Size16.Mail())" />
                                <span style="color: var(--neutral-foreground-hint);">@employee.Email</span>
                            </FluentStack>
                            
                            <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                                <FluentIcon Value="@(new Icons.Regular.Size16.Calendar())" />
                                <span style="color: var(--neutral-foreground-hint);">Hired: @employee.HireDate.ToString("MMM dd, yyyy")</span>
                            </FluentStack>
                        </FluentStack>
                        
                        <!-- Action Button -->
                        <FluentButton Appearance="Appearance.Stealth"
                                    Title="View Details"
                                    @onclick="() => ViewEmployeeDetails(employee.Id)">
                            <FluentIcon Value="@(new Icons.Regular.Size16.ChevronRight())" />
                        </FluentButton>
                    </FluentStack>
                </FluentCard>
            }
        </div>

        <!-- Pagination -->
        @if (totalPages > 1)
        {
            <FluentStack Orientation="Orientation.Horizontal" Style="justify-content: center; align-items: center; gap: 1rem; padding: 1rem;">
                <FluentButton Appearance="Appearance.Outline" 
                            Disabled="@(currentPage == 1)"
                            @onclick="() => ChangePage(currentPage - 1)">
                    <FluentIcon Value="@(new Icons.Regular.Size16.ChevronLeft())" />
                    Previous
                </FluentButton>
                
                <FluentStack Orientation="Orientation.Horizontal" Style="gap: 0.5rem; align-items: center;">
                    <span>Page</span>
                    <FluentTextField @bind-Value="currentPageString"
                                   @onchange="OnPageChanged"
                                   Style="width: 80px;"
                                   Type="InputType.Number" />
                    <span>of @totalPages</span>
                </FluentStack>
                
                <FluentButton Appearance="Appearance.Outline" 
                            Disabled="@(currentPage == totalPages)"
                            @onclick="() => ChangePage(currentPage + 1)">
                    Next
                    <FluentIcon Value="@(new Icons.Regular.Size16.ChevronRight())" />
                </FluentButton>
            </FluentStack>
        }
    }
</FluentStack>

<style>
    .employee-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
        gap: 1rem;
    }
    
    .employee-card {
        cursor: pointer;
        transition: all 0.2s ease;
        padding: 1rem;
    }
    
    .employee-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .employee-photo {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--neutral-layer-2);
        flex-shrink: 0;
    }
    
    .employee-photo img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    @@media (max-width: 768px) {
        .employee-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

@code {
    private IEnumerable<Employee> employees = new List<Employee>();
    private IEnumerable<Employee> filteredEmployees = new List<Employee>();
    private IEnumerable<Employee> paginatedEmployees = new List<Employee>();

    private string searchTerm = string.Empty;
    private string selectedDepartment = string.Empty;
    private string selectedStatus = string.Empty;
    private string sortBy = "name";

    private int currentPage = 1;
    private string currentPageString = "1";
    private int pageSize = 12;
    private int totalPages = 1;

    private bool isLoading = true;

    private readonly string[] departments = new[]
    {
        "Engineering", "Marketing", "Sales", "Finance", "Human Resources",
        "Operations", "Customer Service", "IT", "Legal"
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadEmployees();
    }

    private async Task LoadEmployees()
    {
        isLoading = true;
        StateHasChanged();

        employees = await EmployeeService.GetEmployeesAsync();
        ApplyFiltersAndPagination();

        isLoading = false;
        StateHasChanged();
    }

    private void OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;
        currentPage = 1;
        ApplyFiltersAndPagination();
    }

    private void OnDepartmentChanged(ChangeEventArgs e)
    {
        selectedDepartment = e.Value?.ToString() ?? string.Empty;
        currentPage = 1;
        ApplyFiltersAndPagination();
    }

    private void OnStatusChanged(ChangeEventArgs e)
    {
        selectedStatus = e.Value?.ToString() ?? string.Empty;
        currentPage = 1;
        ApplyFiltersAndPagination();
    }

    private void OnSortChanged(ChangeEventArgs e)
    {
        sortBy = e.Value?.ToString() ?? "name";
        ApplyFiltersAndPagination();
    }

    private void ClearFilters()
    {
        searchTerm = string.Empty;
        selectedDepartment = string.Empty;
        selectedStatus = string.Empty;
        sortBy = "name";
        currentPage = 1;
        ApplyFiltersAndPagination();
    }

    private void ApplyFiltersAndPagination()
    {
        var filtered = employees.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var search = searchTerm.ToLower();
            filtered = filtered.Where(e =>
                e.FirstName.ToLower().Contains(search) ||
                e.LastName.ToLower().Contains(search) ||
                e.Position.ToLower().Contains(search) ||
                e.Department.ToLower().Contains(search) ||
                e.Email.ToLower().Contains(search));
        }

        // Apply department filter
        if (!string.IsNullOrWhiteSpace(selectedDepartment))
        {
            filtered = filtered.Where(e => e.Department.Equals(selectedDepartment, StringComparison.OrdinalIgnoreCase));
        }

        // Apply status filter
        if (!string.IsNullOrWhiteSpace(selectedStatus))
        {
            if (Enum.TryParse<EmploymentStatus>(selectedStatus, out var status))
            {
                filtered = filtered.Where(e => e.Status == status);
            }
        }

        // Apply sorting
        filtered = sortBy switch
        {
            "name" => filtered.OrderBy(e => e.LastName).ThenBy(e => e.FirstName),
            "department" => filtered.OrderBy(e => e.Department).ThenBy(e => e.LastName),
            "position" => filtered.OrderBy(e => e.Position).ThenBy(e => e.LastName),
            "hiredate" => filtered.OrderByDescending(e => e.HireDate),
            _ => filtered.OrderBy(e => e.LastName).ThenBy(e => e.FirstName)
        };

        filteredEmployees = filtered;

        // Calculate pagination
        totalPages = (int)Math.Ceiling((double)filteredEmployees.Count() / pageSize);
        if (currentPage > totalPages && totalPages > 0)
        {
            currentPage = totalPages;
        }

        // Apply pagination
        paginatedEmployees = filteredEmployees
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize);

        StateHasChanged();
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            currentPageString = page.ToString();
            ApplyFiltersAndPagination();
        }
    }

    private void OnPageChanged(ChangeEventArgs e)
    {
        currentPageString = e.Value?.ToString() ?? "1";
        if (int.TryParse(currentPageString, out var page))
        {
            ChangePage(page);
        }
    }

    private void ViewEmployeeDetails(int employeeId)
    {
        Navigation.NavigateTo($"/employees/{employeeId}");
    }

    private Appearance GetStatusAppearance(EmploymentStatus status)
    {
        return status switch
        {
            EmploymentStatus.Active => Appearance.Accent,
            EmploymentStatus.OnLeave => Appearance.Neutral,
            EmploymentStatus.Inactive => Appearance.Lightweight,
            _ => Appearance.Neutral
        };
    }
}
