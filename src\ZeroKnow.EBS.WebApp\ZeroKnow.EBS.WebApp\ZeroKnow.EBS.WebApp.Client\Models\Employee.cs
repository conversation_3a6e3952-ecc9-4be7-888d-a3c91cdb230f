using System.ComponentModel.DataAnnotations;

namespace ZeroKnow.EBS.WebApp.Client.Models
{
    public class Employee
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;
        
        public string FullName => $"{FirstName} {LastName}";
        
        [Required]
        [StringLength(100)]
        public string Position { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string Department { get; set; } = string.Empty;
        
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        
        [Phone]
        public string PhoneNumber { get; set; } = string.Empty;
        
        public DateTime HireDate { get; set; }
        
        public EmploymentStatus Status { get; set; } = EmploymentStatus.Active;
        
        public string? PhotoUrl { get; set; }
        
        public string? ManagerName { get; set; }
        
        public decimal? Salary { get; set; }
        
        // Personal Information
        public DateTime? DateOfBirth { get; set; }
        
        public string? Address { get; set; }
        
        public string? EmergencyContactName { get; set; }
        
        public string? EmergencyContactPhone { get; set; }
        
        // Performance
        public double? LastReviewScore { get; set; }
        
        public DateTime? LastReviewDate { get; set; }
        
        public string? Notes { get; set; }
    }
    
    public enum EmploymentStatus
    {
        Active,
        Inactive,
        OnLeave,
        Terminated
    }
    
    public enum Department
    {
        HumanResources,
        Engineering,
        Marketing,
        Sales,
        Finance,
        Operations,
        CustomerService,
        Legal,
        IT
    }
}
