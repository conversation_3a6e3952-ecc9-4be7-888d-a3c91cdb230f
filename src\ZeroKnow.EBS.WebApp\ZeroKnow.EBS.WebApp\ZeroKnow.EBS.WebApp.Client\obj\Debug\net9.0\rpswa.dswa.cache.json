{"GlobalPropertiesHash": "od7HWwZ5Hmt8YsYbAyCCOg3caNnHC4wt6p46oZTUets=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["2dxsgr3eaJ2xF2a7QACmd/OhOwH44AzhkMEUjMnYqUc=", "hmMr0a1ZLu+XvdiLgvJAZkJGGTvgur7kF2MZA0eiryY=", "WQo+lt8sldGx4MBcY8OTfRpbahY4GNuuLk8cDNCAZUE=", "bS1ZhmQDgdQF8I2kMuGlvRryH2Wy2rtJdLThpb/nu5c=", "Eylg2z9xKqnYn5ETSlonl84G1cv7vuuXyzU4tNrIWLI=", "TXOoFjnj3WMVS25d6cxveYoWrfC6EG5d9sd1Csr1rIQ=", "bxGscpOu5tk8O+VBx3mlt75o5ziCcCkj5TnckP3ygVk=", "Sw2CSJZPeLqU6iFFtcaNf+f4mbilO7ohfaLP6A4/pRo=", "hMS2QUrO7p+USsBvJQGzZOdR+Zfep+NMpY7ubdSw6YY=", "nUWBhQYMKFP38Cy3DQReCnBa/KQ6okS0Y4xvTxZWR3g=", "QdkyPJfDhF+k6OE2tVqu+mw9DGyk0XbnQfHiL3hVvKg="], "CachedAssets": {"2dxsgr3eaJ2xF2a7QACmd/OhOwH44AzhkMEUjMnYqUc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.Development.json", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tswichycbp", "Integrity": "c/lfngzrIF/BxNxQwHaX/Pop1wh4aMKu8dUEyzjHcew=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.Development.json", "FileLength": 127, "LastWriteTime": "2025-08-13T05:31:11.133757+00:00"}, "hmMr0a1ZLu+XvdiLgvJAZkJGGTvgur7kF2MZA0eiryY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.json", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tswichycbp", "Integrity": "c/lfngzrIF/BxNxQwHaX/Pop1wh4aMKu8dUEyzjHcew=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.json", "FileLength": 127, "LastWriteTime": "2025-08-13T05:31:11.1347558+00:00"}}, "CachedCopyCandidates": {}}