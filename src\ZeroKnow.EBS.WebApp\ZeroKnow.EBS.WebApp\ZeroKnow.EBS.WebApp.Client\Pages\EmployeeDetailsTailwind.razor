@page "/employees-tailwind/{EmployeeId:int}"
@using ZeroKnow.EBS.WebApp.Client.Models
@using ZeroKnow.EBS.WebApp.Client.Services
@inject IEmployeeService EmployeeService
@inject NavigationManager Navigation
@rendermode InteractiveAuto

<PageTitle>@(employee?.FullName ?? "Employee Details") (Tailwind)</PageTitle>

@if (isLoading)
{
    <div class="tw-flex tw-justify-center tw-items-center tw-py-12">
        <div class="tw-flex tw-items-center tw-gap-3">
            <div class="tw-animate-spin tw-rounded-full tw-h-8 tw-w-8 tw-border-b-2 tw-border-blue-600"></div>
            <span class="tw-text-gray-600">Loading employee details...</span>
        </div>
    </div>
}
else if (employee == null)
{
    <div class="tw-container tw-mx-auto tw-px-4 tw-py-6">
        <div class="tw-bg-red-50 tw-border tw-border-red-200 tw-rounded-md tw-p-4 tw-mb-4">
            <div class="tw-flex">
                <FluentIcon Value="@(new Icons.Regular.Size20.Warning())" Color="Color.Error" />
                <div class="tw-ml-3">
                    <h3 class="tw-text-sm tw-font-medium tw-text-red-800">Employee not found</h3>
                </div>
            </div>
        </div>
        <button @onclick="@(() => Navigation.NavigateTo("/employees-tailwind"))" class="fluent-button-primary">
            Back to Employees
        </button>
    </div>
}
else
{
    <div class="tw-container tw-mx-auto tw-px-4 tw-py-6">
        <!-- Header with Back Button -->
        <div class="tw-flex tw-items-center tw-gap-4 tw-mb-6">
            <button @onclick="@(() => Navigation.NavigateTo("/employees-tailwind"))"
                    class="fluent-button-secondary tw-p-2"
                    title="Back to Employees">
                <FluentIcon Value="@(new Icons.Regular.Size20.ChevronLeft())" />
            </button>
            <h1 class="tw-text-3xl tw-font-bold tw-text-gray-900">Employee Profile (Tailwind)</h1>
        </div>

        <!-- Employee Header Card -->
        <div class="fluent-card tw-p-6 tw-mb-6">
            <div class="tw-flex tw-flex-col md:tw-flex-row tw-items-start md:tw-items-center tw-gap-6">
                <!-- Employee Photo -->
                <div class="tw-w-32 tw-h-32 tw-rounded-full tw-bg-gray-200 tw-flex tw-items-center tw-justify-center tw-flex-shrink-0 tw-overflow-hidden tw-border-4 tw-border-gray-100">
                    @if (!string.IsNullOrEmpty(employee.PhotoUrl))
                    {
                        <img src="@employee.PhotoUrl" alt="@employee.FullName" class="tw-w-full tw-h-full tw-object-cover" />
                    }
                    else
                    {
                        <FluentIcon Value="@(new Icons.Regular.Size48.Person())" Color="Color.Neutral" />
                    }
                </div>

                <!-- Employee Basic Info -->
                <div class="tw-flex-1 tw-min-w-0">
                    <div class="tw-flex tw-flex-col sm:tw-flex-row sm:tw-justify-between sm:tw-items-start tw-mb-4">
                        <h2 class="tw-text-2xl tw-font-bold tw-text-gray-900 tw-mb-2 sm:tw-mb-0">@employee.FullName</h2>
                        <span class="@GetStatusBadgeClass(employee.Status) tw-text-sm tw-px-4 tw-py-2">
                            @employee.Status
                        </span>
                    </div>
                    
                    <div class="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-4 tw-mb-4">
                        <div class="tw-flex tw-items-center tw-gap-2 tw-text-gray-600">
                            <FluentIcon Value="@(new Icons.Regular.Size20.Briefcase())" Color="Color.Neutral" />
                            <span class="tw-font-medium">@employee.Position</span>
                        </div>

                        <div class="tw-flex tw-items-center tw-gap-2 tw-text-gray-600">
                            <FluentIcon Value="@(new Icons.Regular.Size20.Building())" Color="Color.Neutral" />
                            <span>@employee.Department</span>
                        </div>

                        <div class="tw-flex tw-items-center tw-gap-2 tw-text-gray-600">
                            <FluentIcon Value="@(new Icons.Regular.Size20.Calendar())" Color="Color.Neutral" />
                            <span>Hired: @employee.HireDate.ToString("MMM dd, yyyy")</span>
                        </div>
                    </div>
                    
                    <div class="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-4">
                        <div class="tw-flex tw-items-center tw-gap-2 tw-text-gray-600">
                            <FluentIcon Value="@(new Icons.Regular.Size20.Mail())" Color="Color.Neutral" />
                            <a href="mailto:@employee.Email" class="tw-text-blue-600 tw-hover:text-blue-800 tw-transition-colors">@employee.Email</a>
                        </div>

                        @if (!string.IsNullOrEmpty(employee.PhoneNumber))
                        {
                            <div class="tw-flex tw-items-center tw-gap-2 tw-text-gray-600">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Phone())" Color="Color.Neutral" />
                                <a href="tel:@employee.PhoneNumber" class="tw-text-blue-600 tw-hover:text-blue-800 tw-transition-colors">@employee.PhoneNumber</a>
                            </div>
                        }
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="tw-flex tw-flex-col tw-gap-2">
                    <button class="fluent-button-primary">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Edit())" Style="margin-right: 0.5rem;" />
                        Edit Profile
                    </button>
                    <button class="fluent-button-secondary">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Print())" Style="margin-right: 0.5rem;" />
                        Print
                    </button>
                </div>
            </div>
        </div>

        <!-- Tabbed Content -->
        <div class="fluent-card">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <button @onclick="@(() => SetActiveTab("personal"))"
                            class="@GetTabClass("personal") py-4 px-1 border-b-2 font-medium text-sm">
                        Personal Information
                    </button>
                    <button @onclick="@(() => SetActiveTab("employment"))"
                            class="@GetTabClass("employment") py-4 px-1 border-b-2 font-medium text-sm">
                        Employment Details
                    </button>
                    <button @onclick="@(() => SetActiveTab("compensation"))"
                            class="@GetTabClass("compensation") py-4 px-1 border-b-2 font-medium text-sm">
                        Compensation
                    </button>
                    <button @onclick="@(() => SetActiveTab("performance"))"
                            class="@GetTabClass("performance") py-4 px-1 border-b-2 font-medium text-sm">
                        Performance
                    </button>
                    <button @onclick="@(() => SetActiveTab("documents"))"
                            class="@GetTabClass("documents") py-4 px-1 border-b-2 font-medium text-sm">
                        Documents
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-6">
                @if (activeTabId == "personal")
                {
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Full Name</label>
                                    <p class="text-base text-gray-900">@employee.FullName</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Date of Birth</label>
                                    <p class="text-base text-gray-900">@(employee.DateOfBirth?.ToString("MMM dd, yyyy") ?? "Not specified")</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Email Address</label>
                                    <p class="text-base text-gray-900">@employee.Email</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Phone Number</label>
                                    <p class="text-base text-gray-900">@(employee.PhoneNumber ?? "Not specified")</p>
                                </div>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Address</label>
                                    <p class="text-base text-gray-900">@(employee.Address ?? "Not specified")</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Emergency Contact</label>
                                    <p class="text-base text-gray-900">@(employee.EmergencyContactName ?? "Not specified")</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Emergency Phone</label>
                                    <p class="text-base text-gray-900">@(employee.EmergencyContactPhone ?? "Not specified")</p>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                else if (activeTabId == "employment")
                {
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Employment Details</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Employee ID</label>
                                    <p class="text-base text-gray-900">@employee.Id.ToString("D6")</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Position</label>
                                    <p class="text-base text-gray-900">@employee.Position</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Department</label>
                                    <p class="text-base text-gray-900">@employee.Department</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Manager</label>
                                    <p class="text-base text-gray-900">@(employee.ManagerName ?? "Not assigned")</p>
                                </div>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Hire Date</label>
                                    <p class="text-base text-gray-900">@employee.HireDate.ToString("MMM dd, yyyy")</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Employment Status</label>
                                    <span class="@GetStatusBadgeClass(employee.Status)">@employee.Status</span>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 uppercase tracking-wide mb-1">Years of Service</label>
                                    <p class="text-base text-gray-900">@GetYearsOfService(employee.HireDate) years</p>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="text-center py-8">
                        <p class="text-gray-500">Content for @activeTabId tab will be implemented soon.</p>
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public int EmployeeId { get; set; }

    private Employee? employee;
    private bool isLoading = true;
    private string activeTabId = "personal";

    protected override async Task OnInitializedAsync()
    {
        await LoadEmployee();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (employee?.Id != EmployeeId)
        {
            await LoadEmployee();
        }
    }

    private async Task LoadEmployee()
    {
        isLoading = true;
        StateHasChanged();

        employee = await EmployeeService.GetEmployeeByIdAsync(EmployeeId);

        isLoading = false;
        StateHasChanged();
    }

    private void SetActiveTab(string tabId)
    {
        activeTabId = tabId;
        StateHasChanged();
    }

    private string GetTabClass(string tabId)
    {
        return activeTabId == tabId
            ? "text-blue-600 border-blue-600"
            : "text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300";
    }

    private string GetStatusBadgeClass(EmploymentStatus status)
    {
        return status switch
        {
            EmploymentStatus.Active => "fluent-badge-active",
            EmploymentStatus.OnLeave => "fluent-badge-leave",
            EmploymentStatus.Inactive => "fluent-badge-inactive",
            EmploymentStatus.Terminated => "fluent-badge-terminated",
            _ => "fluent-badge-inactive"
        };
    }

    private double GetYearsOfService(DateTime hireDate)
    {
        var timeSpan = DateTime.Now - hireDate;
        return Math.Round(timeSpan.TotalDays / 365.25, 1);
    }
}
