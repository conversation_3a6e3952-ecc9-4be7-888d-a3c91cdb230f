{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["/P1ULRpJiVWMlpruL3vWuzAUUWwn4QIKOM5BWsrfO9E=", "6UR03JYdwIAxL38/eKiWegTXCttfY64CTXEhoVXOpsw=", "0omwWbvbfrqKqtlLoeeGWROqeWmMugJqJSoNH7hGhxI=", "XEgqGddGJu866pOogP7KcjTQCc1LWF4ko7lAFwxFdsk=", "iERuIt5KTWy7FsdD+5SYsMZvX60XiqrWrwnjUpgzSsU=", "3BftFgQR01KYgu1In+If4fNs3WAx7iayxCR4WVryd14=", "DCDqij06uWZ+4pLdRVWxisnq6/1XDV79y2FaRmDKlww=", "6ZBaMoaYjrYyp2c5RSwM/59cn0DR5xunjAhVK1C3ZHM=", "7paA47+DYVdpKtEbl54A0fn4Fiy0RNu0Skk7JvFlfX0=", "+sbTmEvz3sex22KPkeej9+I6w6A4fEVSl0GPb+el2Cg=", "CXaACg4Y9+xVeIr3O0Dmv41MyRiv+sisEVtNRj7L2HY=", "pVkRGc4qYMAeyDwnep0vOtB2Phvk4kDzSMgyshhnueY=", "Cr8DT71iaG4fLsEUGeqmd1cVlEjqsKnNZb5IKUypqko=", "mhxS7sxsaUFWGPI3AlOs6g/qWfzi6Ke7IP6EYqaQN5Q=", "xtrbx5QsAoejTWrwEB66Xxq4dR6lmRfYKbpwqX2h4H8=", "Or60A+6M+ZZZS7603W13qsRmTJec9MM9waIJAdMf6Vs=", "vd7XfUtJ3fIk61u5pLRFFY2OGz/u7TZKyDM/Hs+9Wzw=", "rUorUef7wNiTO9R0UuP2nUw9Be07V04vb99qRunhF6E=", "OkPBjLlkI6g5fP0wLq7nG7xZsfkgxiAcxW0O9yF4wzM=", "MdroU9VkeSYl0jyYAeogEudpF+KgAkE9sP9BEBevTb0=", "mZjS8xr58xu85+KsVs5NaLQE0W5N383mR9IHWqeFBeQ=", "8BnNJ+lxDuAdh4mtfDDjXUBvfk/lY2sEj8yybyodj4A=", "QRCBtprf7Gv374QUZlDO0aSuTA4skMdkQ10mjCzjRdQ=", "x924Wj+Mlt7S9KZgGtA5JpGtSxHFJeQdH9Z2S/r195M=", "H44Wn7KomEcY+lQZJwOnfGGz2Zr6fOKv1OJQ7gewQ7A=", "SSABqBE0i+jXWdbsBidCBwHd+9wRnbHZgb3wg9ILock=", "W1NQMbK6zJkbe35/tFU8Xb+MQyC94G0ZFMkt6hvRDGw=", "KgG2pvn0pp9XFPN1H/fqyQ1G1bzsRoIYDIcq+w2G+GI=", "rve0N0yg7/JwjOO6uswABddIjmPscOZsgzNKLSWkSUY=", "xKTwHOW8jl5MQ/Yo7NLHL9XkvNUok4JMZgDy88a8jxc=", "PnXEYMLtmqFN1GB52JBwkrRMKE8r7OEPVURAT6HOjvY=", "IsyT305m2mlcPxcrDH9U6lzOJxL5TSMs+vMsjks3xgs=", "KeGnJOjSuNKfRI7Apk1xcQUVB2rJLLWxocJR6mc/AMw=", "SUGr96ZslN/CN2HlO5jRqOAvAMzjFfGSMBrPv9ofuTw=", "kXNntqH1MnlR7WJycsWNtoqqk1radTUu4RW7uYld1oI=", "5id0QEghPa6herr0vZ2SPXR5hMr1FtTazLG+sPwVyJU=", "E4eEpI/8rMbtxXBaBZOyL2KXMZ4B9fKinu7FA4Q+YY8=", "wPf50sGzbsUr8khd5PL9WqHRxW/+md3kqROar7lJrCE=", "Msg6dRgGitE5Zoziacy+xU53rMGq3bMyC0X6XYaYIpI=", "2gY2/1qI6wiNEsKFl20vCGe9zf7MR74+suvWcqnV8G0=", "HH4cg0dMKl9OF5fnU2G/gkWecl4/B7wR9Dz2LciWUHI=", "7qRl01rl3ShYRW80vpzRCBGRn+sQ1bFzSJmYi9lMzRU=", "VxlbjdxWpGn6IIx+d81ZwXEGDx+jDemVqadpCZsiPz8=", "w4f+EPL1AnsQHZqbMgKGlIzLJgcZvZRl8umoRE7DsPM=", "9n1IDvVzlDJg7EjG2etRlLpVqM51JgJ+fJ4SHBJ5msk=", "kykCokObg0rl67KCG6srVHA8UdSeds3z67UJf2oZeU4=", "M+ux8LJol/bYB3BHjzjOtOGu9rqrxEemVEE1/AsrRpw=", "9Sx/4FB2cELiMtHvEGzspzyDMI6qFTiMBreZBEyWO18=", "5Z1PGtMQ9g1v1DuHIZ1MQGe+rA/s8FqBibCHdReirTs=", "0woO01+pp5uVfmyXwhcyYj9DGWeM+HB8AMJa/TYfNgQ=", "iIZvR8BPhIm1k3lWMLOngHmBHedZ8SzXUEob/qtcs6U=", "qjlqfroNOgBeYV/66ilZ0KeXn2KEwtXiUWN6jlI0sSA=", "GLf6mudFyhXqBhoILz3Qiti6nFe4/Ta7CHmggIdGtB0=", "VC03LisGpNZcQ83U/wcgCGuvkXnKDBNeEPtWoOejkvQ=", "ypJST/lWB/aOh2z1eRA6py605gj34iMjYNL3TJWwims=", "E3kZxa1OKbNuYx/i0hkFIodKcbngFrQM7WyGYNxHOhw=", "gNhJsED23/h8ObAHfAAAhNuCgZsUPMyZDDi2/c9PR/M=", "QJxqNdMsBphDLdoq8QcAgnxCezsztr1GsVPhHL2kizY=", "9sN10TZa7hAynwRmGAezn3m83ryPpb77boI7FXgcZT8=", "pFcGdKy+x0y8JjLN4XOUZyhq/nQN67QQD68duEEMYac=", "X6gWk0JLBwqCpQDDtG7f67vWgA4MPICQqS+IUc5a9Ds=", "rzlYZ+/XuW0+ThNw+zeLJO+Ustd3AC12wyuSk/6nUus=", "75m7TDYTYDPC1/dbZeV0GE4ZYrJuVnS1W1YDhN5ZRw8=", "yLbzcQaxC9aK5trlsuobMN2n6ncZ5TrwphX62zr+5WY=", "l0yG70wLBBRDe/N4JIleyFVfS/R9ma3ELwUEasvKpLE=", "JValv674CQICaWQfOsDjei8CdrWovBxMb4Sg1WUbF3A=", "PhJQCuHhOtyUE/bEB/BjQU/eItOeuAA9/MFiqpGFzRA=", "AtidhynyE/+bJL8rwrfP9pFCIJdRn2H0JXbgLBqTmkY=", "OV8gdPhrO60LaNAdtTCIUwfMMChzrvM8eMeUAgy4+SQ=", "AnLmavUF5z2ujI+d2YKWV2SnfLTTfSMnIgUSCU3zUfU=", "xle7Cy0QfzH0XhbX2Wypphai93L3ulqYIzA7Ul871aY=", "OynJtTiPN1rCLqmqtZHmGi87kqzbHNJm//I4nWZZhrc=", "kvflI30o9uf5E5M6hEhHyZoRmcMIWdtmtVV6DSsqXWw=", "EdbUnh3npZvsSq+y9jX6bZ8YtXh02KNXgbdWCxABp8g=", "PYI0uc9ttR/YOc2H9Rj+WoTPPnkjuvXlpffhRnUNCfs=", "GC+2N23nGaKG8GZ/Bjo59iGjD5zMe+Fto2nJp/ekahs=", "32+mvPCUmvF9Se/gM+SIMXAnlN43HOYUqpX8CxzgnIg=", "45HlwefJOuh5LqjWcuoJVDRGMYSXKxPfK9qyeknH+Ao=", "sfhV8x4iQlAGrXS0lihnihiV4fUZDNMvcpQaCCHV/BQ=", "J2lXj1C0VT3aSxfZQz79NK7f6y4InMIKNrmaGLgPuj8=", "7prl076/8gxnfm52U3ZE8gBzyUza1nO5208z68lbI1o=", "vbJJF05uYYihHxCA4kD0/Oasa7T+QrTEkYVs8e5z3mE=", "jejzVL9jTZst8Npdb9zn4o2MTiRIPMbibec8oOji1/A=", "Rciu4T7DMx5I2Z+2pIjjSZZ2AdkWb+cG0QEjsOeILyI=", "EMYYBOfY+z626L9z6Or4FHCBcFI2Nyi3HTzN03fKasY=", "Fdb6zbCxCM3khmSRW8TiVE1qA2jZsbXrtlxsP5ndIkQ=", "OrjxlOyfGvv+2yy3UnN3OOlISuEk5tY+XbNoSWTBqSg=", "cvMHg7IGEsNjfCRjtOwpJIHyUQ333wXNHWUNU+QBq1Y=", "KKelSrhnsOAebbmBphqPOuqDXs9iyoX9HhdRoE4bff8=", "PdxfLCHyp1gFlvJWtOnG/I66wI+U7f7smPiSlI7IsB4=", "wU1DWRaqASddTdyy21EPbSvX9wmGnIMpqyvTkqJXwh8=", "YtCcqLC/s83o/Wtyt4NrlC9mvvuna3sWTjRGhMjNFXs=", "8PusHNK3RNCfLR1qEJUiwpRSxcBYKia3JLEo2s1tops=", "5PkCOZ2Z6f6W66xkHErrGtg+btU3gXRuOoo9Kp5r3PU=", "bYlYARxgcGI82uWVgvilNpPoQBgAuwNuAhFi7NZtjfI=", "2NQfMHtNdv5wOJBvKapAbdwvticgmxVYdw3sb1pWONI=", "A7Gz4zBdnXreT6xF7l4keANl5KbbdK5hlgYNZnduBok=", "lKryBiI+wmTDmy0jNPX1D4eo0jOqlMeLW8pA5EZxpuA=", "wpUtH5HyhelRsPLJw6vDQl8Srai2g3jOSxfxO+Is9yI=", "uz/N/DWAQIFkHYawXIKH5hqWkOyFMAn7HWBBukQVDWk=", "e70it1YeE7LIiaa5/ZUljRIYIz2v8r/XJFFqKn8STLE=", "PMB1QTzA1u4jmKBpmaWxk6SaWsy8H64sgMhQ4QKyqbk=", "wfKHCHEc7cclnCAOsiHGnbQ+5uZ5ck+iKmiawgjFw6g=", "iKYXPQihldRv0FYh/Mpi5Rh3wAuoitgV+CoUPvaXyas=", "gDXqJGeXj4A79/sjGQlrxXhw8OV2f0KOZQYTfpCYtX8=", "W20yQYpdzdAVTFqvmG5nUDICfX/dEJt4Ke6ifDmCv/I=", "IH9EGHgTwmDYnFkx+tR/vqFsWU2DFYxC0iNK3lBZFyc=", "lDy6YMi7psgBIKyDwmxi0wpgczuBmMbbO78W5Wsuv5Q=", "V96tr4aHj5ZojmlBmPRBM7YNo+eKkqXCuNSQWP8/HRk=", "xCXFyoRqFI+KjPLZPiq2gfwkvgaoyF2P896+lw3jYh8=", "bq6olkjLx6XwMAAokdiDHr2RFgDps3Gz/DjBLyYUNvk=", "qP+mO+0/rx5ucYFgmxpESybcjMcHiE99m7OTp0207so=", "b/DJlxO53ETv+KS7y+NwQahlaFI7MUAPciIQQs12GFE=", "Bvdbn68ous5b3S4hnEo1bY/XhNrU9ABJMc5+GjGmcMY=", "8g4QuO5DnLdelcHGJYTFENpPoO6JQppx8G0Iq10cIxE=", "VCNWlule9SDgxPUU/6NsAcldotfDaVv1Ij7RaqG0NC8=", "EAjABs7qCivI5S0a0wRXjX4A4Zbf9bDwnbq+TA8tIuQ=", "CYW/aARhYbUfZs+VYhIU9GSl4j+e60rv/ZyJ05gdkNk=", "p19k5eIr7rXmIi+Urf1iin/H08f7dZ31Y5eyOmZR6Z0=", "UfgG/2PC9q7T8qoeR/KU9L67NREBtG8gTsD6H5/J/PI=", "m6xi52J6/I7Yh5C26lfbD2/JB5WbMBDi3TRTFy/s5Vg=", "m1CZyA8AsYlG6ZgdEjMrHfcl/GGGoIrbuG6Oe0Z8wo8=", "9nZnPj0XAM54g2JxMqEfqq9VP9znA4yFwNX72/QJ7p0=", "zf1HpSnujL51rKblZ9KuP2GK8DaJXmywkGshng+Pp58=", "TdCMW8/czHdSqSnQ5wGtpVjAaaNuUVNwvDxLcsVvkR8=", "IiQVWJrmcJ4gQbBIaS3Wlt4W2bjCF9srdXUIPGoyowQ=", "n/4penQXFp2P806iLq4uInF5y3DhGHEcI2RRwWmnD6M=", "jO4oeICK36V3GFPpPgPoxqq+HTO56NcHb7zXQFR90Rs=", "MUNvANiCLzBQce+hAa6j0bu8k7yX0QkIAeGNpaOtYu4=", "57Hu2bG1dEQ0uXINuvY18KG0aS1ghfEn20NYmk6Wi+k=", "EzlhFIbmti3uax8j8TSwN9W2fvBnhqpGuEpPKRGi9pM=", "pMk7TuMbIGTz/vF5RmjmMl4xtcft7vQkbKEydGoInC8=", "E1KLFHe+r/yodn4m2i5DBWc1fAc0xbrHZOfVGLZRRL8=", "rkDMLGxlN95/3ZO5RaxXwXrCpRLEPO5t9dSeq2GSTQQ=", "AMr07vfqHtW4z/WS74KUGC/tBQe1Xmi+LxlHuQNQR8Q=", "CZ8wGjLcXi2zLssAXh9r6YxfevJYjDKjvQbLlNlhWjo=", "g9IL5RFmGsZRLJwXT8NYva5LT2B57+0K1uLrOSoarR8=", "qvGco6T+CIQyqxdxeAPPuKWwgTegyMJZYNFs7/wa81k=", "tNs5PnYbtGRGnX0qbHD5IKa/imvs5BXJi/2vdR6/II0=", "fjCLghrQR9sGvsmN5kBbDfEFFEYQyWQqCAq4+8ugXYY=", "nK7LdJPdStmejkaWYl1LQb784vsdOmmaRoC2P1X4vZk=", "qAVv0A/SPWXElvtfFbJvrpv19AgRHZKsmsvIJG/ak8Y=", "MK6LPRlhdlKRtMA8byK3zltna+nzDe62sLgtVdiuDOU=", "FR77C9euzs5iWXMPfgtx8QyvS2qPkbiuoct/vWXn13U=", "LP5BiuMFW840K/BTZ9nvp90T35peuhQZc7GduKzd9Js=", "h1BcU933SkrCmcYHhqswkPSAyJn8KrOI0/32DOS719M=", "355I6l7H1bt+1z91sdz/h3AyceuObY45SAir0vzp088=", "zkeQxMEijQThq05j7xfn7SaihWrH8Pv6cUw+/nz/DAo=", "x9VlOGbALE2gizc9hBq+giaIBoh66pbHNZiK1/rTt6g=", "gVQsFGRDYCirczC1Dc7958wFMOTFAHwzetgfqFUfokM=", "5YsaoIbIaOXUCwcqjudwA5N9j+bYkuqbeNQkBcJQr+M=", "yINjDwORhU6xk2QoiQ2YxeFHwtZkNmYlAaClKH0njsg=", "JJ/f4WZc7/qC8UyaYsaWx1fYWirgCi4dIffIM/3/0Pw=", "xXHipFeKbbQt/6RaAzxZSXdtGvLuCNCp3lcjW6+CJz8=", "nKRLFMOrWVPaJhYupzmRnd7BPBMCSSkj+dW4AXspnag=", "x0kuOEeUYzPADwagMqEkxGiiFl2pJa/iZ/oAIU3GhCE=", "0i3TDw3yMMWap/04WFhB8PIm9sEDA2oykiu9QoU7qwM=", "AKkgqnfdOPuWtD0u6gldLKz70C9J1wdeNIdmETkiGuw=", "kL5hyxfNaaN54yepc89FsYeFKHzqJGgyB7bYcjvOHJA=", "jbjdEZ19ax3Do5qFGQ1hHmUSs8zo0+A3+AYQZcBpJLM=", "8+upP30QV2hpnpJQt3nlcJWamKs8QmFo0/CJhkyOZXc=", "MgVQpK6UPnd/1oSuNvqP73KwHum1X2shJvclvjZpJf8=", "mKr7Q/wI1G66KSILIdh5mhbUUctmkAkebX9UqrCTxyY=", "lcKBDAoXjMv0wAg6rCX39jZxwE9jeoVQAYVkbNbIIRc=", "P6NGYiwgpfUlKKabPQqiPgPnWP6jBtnqwBcF8r8mOow=", "0lU5FN6w69FohSVp0ZbyBfiYC9L3lc+i+ukWmKjRLWY=", "WrZWGLw1T8rcXJZWhHsoE7TL958g20gfz/NzXanabV0=", "COCf+xrMVkCTcDANNbZ5JhOUYVnZAOYGRgCzqMOSFvY=", "4phogNfhS0Zd8LpXFvRISxyXChISVHuhNeh8M0OIrU8=", "Q3uRUTD3WlSdwQc1ojcD+7xOnRFgdQ+EdzRmWn0EZqk=", "gQ+QYcsqO8ruDXbg/6jFGmrzoDH0T32J11Dsk3xxBXw=", "zFE2PDGJBFiCHZC+Qh9ynmzGajXkMkTFwAmp7TB5j5I=", "Mg8S0Q26GMoYwH/I5pg3ziw91h+8M/c99h7HNshYJU0=", "THZejE8EZ5P1ON88N9CyCCh1vPPPDxwxo5mKRWqkJrw=", "N4+VJaLc6ORsEi/t82HCb3TVNn+DJxs2yZKI7SLwN5g=", "n4gmCHnPvxDLeJCKAwYHbcimz9iY5AxtFUml+J+0SlA=", "VtuRBpqlR4UQZ9fl8w/J5Qx0/M0QsXr16rrA2/PIhm0=", "iuW4HlWFk8QdN6bgQaEu1nuRJxN4RzOhwUAfv5VKrl8=", "fFRk5iqchaOV2k7tim6fjxgwZkEt2MmCHm6NK5ECWC8=", "2wY1BZ3FGpyw6TBgWragqY4s/sx0UeyKFcJAodvfboc=", "/9ho7gJ9O9PWweMILViGwec4DJof8NyPNUy/pk1anQI=", "wDnRDfPrZKuiVX8khLk+nCPAmSGpstvBIz42p/+UrpI=", "fHN2amvazD9319nvEA59O8jEDSlwlzXiWPyCq7u6iXg=", "JKWJ32elzs6vaEmLPIZPJsbgfidb/hN7J79Jr4K5Y68=", "Heh4Jj0IHOuajRgveUM9iXKRIRZhTjmDBQ1DacV3lv8=", "miqkKdtipdqFCh2FaBSaKYNeTS+8V5qiEMuwaf6V3pY=", "tgRjwMd9N32C4gLX7n+CTbznWYBwoBmZblpzGF0VtfA=", "CJ01+B/ZnXYOXalAqwETlHQVWxfvZgqq9ulCtPrsKEQ=", "P9KJJHEfedqOgrOWDA1ICoxVVByLnbwltx73F8dPmY0=", "+0Uds+4kzQQ8Nj/8MzIG+EfmkxgDwe1v/DcmoEXFDuU=", "j3Hltk8AW97DHcfVQlYzJGXv5fR728nKXbRg4rjtbHA=", "G/DN/XSnlvQrU7WNEmN9CDTiTTQpTSe51WJLkXuNI5I=", "RQVsRsP+MNPqSGS3dKOnsVf7FtnBvAkZXYWdvj0iYoM=", "WH9AvpRkA6ZBBK20X5FRywNcx9LxwmimYNWYmKGwbWE=", "kFV1c6CViehU5jauPoXsDzFSdeA4WPeXuMxQ9Se0GCI=", "pszna5iXTryCet5CP8URMABwRLgi/PEEs48lvneVgus=", "g4m4AV4nSzJT8fn4lrCVTGVoZWLoVwxO/PVqMMb+GY4=", "HpjElOvDWnID5zM2pjpGjgqaJgGve1AF9nqzSTeyk24=", "TfFadMtnwg8parqm4/c6+Ka8r+nYQPdk3zZgxvO0hlA=", "FaCvrFUCHMHDoYvNU5LV+1Nl5+IivBhFwpx/c0zEp+c=", "oHSpQrtc2cTXclP/JyG3MjmZ2i+h9O3pWkpE9C+It8U=", "vZRGMkF4bzy3BJ24cy0N5cTMnK0uiQF2pVy1QM/Eq9k=", "1MlI3ON0H8xLmHoQSUdnW1QydT+BejEUrtcg/q6HdHA=", "PtEiOINaLTYcuM4nRyUmlAS947Z1WwfUcAxE/12T2Fg=", "c6ZqZKeal/VGj8EkmGwN18ZT0lgDk1Lb6nhdeLz/b6I=", "uuDrwmb7eLsAJzZkVERHrDpt9TqkY1xd+d0L+i5IHB0=", "QCtbAz7U+Y6j8TDnXDvS3aCC/zik8f/4TJkQOV+oyrA=", "eLk1oGciDumTkIMLD0HB/nOKYh0nJl8TFQ4fe/TkR34=", "22m9Nmw+wiAPL305ksKzWZMKnJWfVAqdBPglwUAwSRk=", "iXTiUOZrsVX2uoTFml+YD28x86bu69FTaFtU2eU1QQg=", "c3KqTBQv5X/SOw7NhxTqVW7Ju/0Dvb8jMiuhVSoerVk=", "jc9PHXx9XyZvAPKLgZ/Z1mFMeiK95olwGw6xiqtGeug=", "1ZzNt1aea2c1l88gW0ff4agCU4e6iIIe6aWUc+MjA6E=", "FsIXAUGtjadI2hDJTCUnI9u8+OJ12+UvUuEjetSXhLk=", "JAB09DK8uIumhg+I8f/DBHKOnvRj1i9MoOgYottqW1Q=", "wkZqSXmU25AN6CaFCv/5uCgct8XjhkV7QWHv9Ksz8aw=", "5elvW4VSlZFAC4TGadF1CUUvydbYrfc/Iu83ekWeuwI=", "mA1+ARRoQxUFuWlijJBWyYXkla/nNmRUA7apHmS5SJU=", "J+RLQO7Wvc72LVHVUn2HSGL6fU+sWu2BS/O7kIN3uAY=", "u3J8Uv5GXnuRtBkln2lyem0GGwGtZIDtZOcQoNYq3OA="], "CachedAssets": {"/P1ULRpJiVWMlpruL3vWuzAUUWwn4QIKOM5BWsrfO9E=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\58gft4q2o6-{0}-tswichycbp-tswichycbp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint=tswichycbp}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.Development.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18s7itl1b8", "Integrity": "X/+i+NVLERSsvyIbCPwVbpCnFJxSjGtBN0hXRQ/QJ3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.Development.json", "FileLength": 110, "LastWriteTime": "2025-08-13T05:32:29.8651249+00:00"}, "6UR03JYdwIAxL38/eKiWegTXCttfY64CTXEhoVXOpsw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\k8cprpwkws-{0}-tswichycbp-tswichycbp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint=tswichycbp}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "18s7itl1b8", "Integrity": "X/+i+NVLERSsvyIbCPwVbpCnFJxSjGtBN0hXRQ/QJ3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.json", "FileLength": 110, "LastWriteTime": "2025-08-13T05:32:29.8761246+00:00"}, "0omwWbvbfrqKqtlLoeeGWROqeWmMugJqJSoNH7hGhxI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\n1ecasqnz4-{0}-md9yvkcqlf-md9yvkcqlf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tu2qkcmwci", "Integrity": "C8sFFlTouzLtqtRyeQRNMFLPF4W+BCiy3mRTsrDEK1U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 18006, "LastWriteTime": "2025-08-13T05:32:29.8821247+00:00"}, "XEgqGddGJu866pOogP7KcjTQCc1LWF4ko7lAFwxFdsk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\s4af90zfta-{0}-mnek33bc45-mnek33bc45.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "ZeroKnow.EBS.WebApp.Client#[.{fingerprint=mnek33bc45}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ZeroKnow.EBS.WebApp.Client.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mzo2px474x", "Integrity": "/jfm4gFZpQGbhj5EHhWSxXQNH38POBw9qHT+7gUzenc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ZeroKnow.EBS.WebApp.Client.styles.css", "FileLength": 109, "LastWriteTime": "2025-08-13T05:32:29.8861239+00:00"}, "iERuIt5KTWy7FsdD+5SYsMZvX60XiqrWrwnjUpgzSsU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\8t65xramue-{0}-bvu82j4ad3-bvu82j4ad3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization#[.{fingerprint=bvu82j4ad3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rn7ywsb9ec", "Integrity": "uaQ1LS1jmFmZzh352GTAoJXC27d1XRYZDoQEPFlsiaI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 17687, "LastWriteTime": "2025-08-13T05:32:29.8931244+00:00"}, "3BftFgQR01KYgu1In+If4fNs3WAx7iayxCR4WVryd14=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\z4exqc4shi-{0}-ptfrz3fits-ptfrz3fits.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components#[.{fingerprint=ptfrz3fits}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4u1wmazeum", "Integrity": "TL/RI2mglttuA23zZN+3As8ND500F7dvLyGQn7Xe1lw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 132526, "LastWriteTime": "2025-08-13T05:32:29.9131238+00:00"}, "DCDqij06uWZ+4pLdRVWxisnq6/1XDV79y2FaRmDKlww=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\g1fzn7itsz-{0}-e4o7p51zuj-e4o7p51zuj.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Authorization#[.{fingerprint=e4o7p51zuj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dd0ukx352f", "Integrity": "n3oI3tYbfou0oVhql+iLNmn8YMq5YyntP8rmtyAJbN0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "FileLength": 9860, "LastWriteTime": "2025-08-13T05:32:29.870125+00:00"}, "6ZBaMoaYjrYyp2c5RSwM/59cn0DR5xunjAhVK1C3ZHM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\3fzy8q7d4w-{0}-73oi73dvgk-73oi73dvgk.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms#[.{fingerprint=73oi73dvgk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d75ticaql6", "Integrity": "meE31nuqz9R2oYPZOEtKSHF3XFPlsTCkYGsBqmm7dFo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16299, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "7paA47+DYVdpKtEbl54A0fn4Fiy0RNu0Skk7JvFlfX0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\rlot8jbmoj-{0}-pm8mpy5cip-pm8mpy5cip.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web#[.{fingerprint=pm8mpy5cip}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21fcg1h6jt", "Integrity": "Fhsan/uHdrVJPrYtZJ7AdY+/Olc7tnGsiQjT8SU3N6E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 70894, "LastWriteTime": "2025-08-13T05:32:29.8931244+00:00"}, "+sbTmEvz3sex22KPkeej9+I6w6A4fEVSl0GPb+el2Cg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\z265ud4v46-{0}-2yt2k81j3x-2yt2k81j3x.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly#[.{fingerprint=2yt2k81j3x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o5956j927o", "Integrity": "nL+yTALP41d/hR9znqDm2fd+rDWjSjk6ssnIeIyVp5M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 65863, "LastWriteTime": "2025-08-13T05:32:29.9031254+00:00"}, "CXaACg4Y9+xVeIr3O0Dmv41MyRiv+sisEVtNRj7L2HY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\j1nj0bljxf-{0}-hlbn62k9y7-hlbn62k9y7.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.Authentication#[.{fingerprint=hlbn62k9y7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.Authentication.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "up1onx6z78", "Integrity": "TiYuGetkzqSEAuwCT4h5mjX+8o/e5C4mtWO8tIrtjUA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.Authentication.wasm", "FileLength": 38030, "LastWriteTime": "2025-08-13T05:32:29.9071244+00:00"}, "pVkRGc4qYMAeyDwnep0vOtB2Phvk4kDzSMgyshhnueY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vhgeetol6t-{0}-eyher82q7e-eyher82q7e.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata#[.{fingerprint=eyher82q7e}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jj5cwd5cf1", "Integrity": "8ZT///Rb87GeFxRs8ownOt5YfUiDt14xFuC+fwwi6eU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2408, "LastWriteTime": "2025-08-13T05:32:29.9081247+00:00"}, "Cr8DT71iaG4fLsEUGeqmd1cVlEjqsKnNZb5IKUypqko=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\rr7a4z0g04-{0}-itm12vk377-itm12vk377.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration#[.{fingerprint=itm12vk377}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "47xmptjdfp", "Integrity": "kA6glfdKoe419kfVHMWH24oM0VrZlbfJ+1poRVIX8qM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15536, "LastWriteTime": "2025-08-13T05:32:29.8681249+00:00"}, "mhxS7sxsaUFWGPI3AlOs6g/qWfzi6Ke7IP6EYqaQN5Q=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\3yrm44lql8-{0}-8ewlps0g9m-8ewlps0g9m.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions#[.{fingerprint=8ewlps0g9m}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dow44okadv", "Integrity": "bSJ+SFJ/bScm11NbnU36NJhWLFBQNJOuoTFJ6qLV9LU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 8297, "LastWriteTime": "2025-08-13T05:32:29.8801247+00:00"}, "xtrbx5QsAoejTWrwEB66Xxq4dR6lmRfYKbpwqX2h4H8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\z03dszrn1h-{0}-yr6bnfroy5-yr6bnfroy5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder#[.{fingerprint=yr6bnfroy5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "96l2cly7lv", "Integrity": "T0gWzb1BTmawe7uWGA2eEKqwVDRkNOmQW/QDVgSF6Ik=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 14557, "LastWriteTime": "2025-08-13T05:32:29.8831241+00:00"}, "Or60A+6M+ZZZS7603W13qsRmTJec9MM9waIJAdMf6Vs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\oaq38xm4n3-{0}-6zj77w12m9-6zj77w12m9.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions#[.{fingerprint=6zj77w12m9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ppjsgesydd", "Integrity": "M2XhdpebalDM3kMTRkCMNCaON233KnIWU/3zOKbhYiY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 8228, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "vd7XfUtJ3fIk61u5pLRFFY2OGz/u7TZKyDM/Hs+9Wzw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ibs73fqode-{0}-rzh7ctjkaz-rzh7ctjkaz.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json#[.{fingerprint=rzh7ctjkaz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "419z5k0n7g", "Integrity": "iDIDniCwm7L8G5QCsYYo2ebM9mzT0RTsYpwcuKNKv4k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 8040, "LastWriteTime": "2025-08-13T05:32:29.8951254+00:00"}, "rUorUef7wNiTO9R0UuP2nUw9Be07V04vb99qRunhF6E=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\2rdtolq0jn-{0}-v66dtpac4v-v66dtpac4v.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection#[.{fingerprint=v66dtpac4v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dkwoqybme8", "Integrity": "I/OMmyZYJZeCzJGlYBCulvwLuLnpQzjuMyQeOFAYZUI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 35425, "LastWriteTime": "2025-08-13T05:32:29.9011249+00:00"}, "OkPBjLlkI6g5fP0wLq7nG7xZsfkgxiAcxW0O9yF4wzM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\nljhi7t2bg-{0}-apuz8nsfml-apuz8nsfml.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions#[.{fingerprint=apuz8nsfml}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pcx65pklbi", "Integrity": "WZf6gUcVZKdt3yMmpALH0MfBLIPdkDDh8GWnoPhvOTc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 21605, "LastWriteTime": "2025-08-13T05:32:29.8711256+00:00"}, "MdroU9VkeSYl0jyYAeogEudpF+KgAkE9sP9BEBevTb0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\pe89m5fh0l-{0}-1bmpr6w9dd-1bmpr6w9dd.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Diagnostics#[.{fingerprint=1bmpr6w9dd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jyjyxjvf8d", "Integrity": "izbFAdNl2kR3p4dbfThla8HRGmCuPsSkPcQaxMNdSL0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.wasm", "FileLength": 12345, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "mZjS8xr58xu85+KsVs5NaLQE0W5N383mR9IHWqeFBeQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\naqbf7eh4q-{0}-2lb9066vyq-2lb9066vyq.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Diagnostics.Abstractions#[.{fingerprint=2lb9066vyq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8do5k2zqv2", "Integrity": "nJbCN3+H0TIbr0tAbg5kpk09eMmOHbse4zmuP99ti1o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.Abstractions.wasm", "FileLength": 8786, "LastWriteTime": "2025-08-13T05:32:29.8841238+00:00"}, "8BnNJ+lxDuAdh4mtfDDjXUBvfk/lY2sEj8yybyodj4A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\pz9zp7w1cy-{0}-8bt7as0i9i-8bt7as0i9i.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions#[.{fingerprint=8bt7as0i9i}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "73xrqg8xww", "Integrity": "QzqHZH6Nf89y1+mYIsDTZOB1Ms5ojwvpdSu5uRcuaJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5629, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "QRCBtprf7Gv374QUZlDO0aSuTA4skMdkQ10mjCzjRdQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vsv0op328c-{0}-ily916jl2z-ily916jl2z.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical#[.{fingerprint=ily916jl2z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gq7399yqxd", "Integrity": "GKmvFiIpQHeQoAwpVFfbMZhtIT/jBy/+c6JxVLcoZO4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 16896, "LastWriteTime": "2025-08-13T05:32:29.8951254+00:00"}, "x924Wj+Mlt7S9KZgGtA5JpGtSxHFJeQdH9Z2S/r195M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\d8u97go5m4-{0}-sdsdr06lyk-sdsdr06lyk.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing#[.{fingerprint=sdsdr06lyk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c6bv30em72", "Integrity": "rHKhWpD3rpzzadB2BJRoKYlTyBArGwzfeKmz3SYbc2Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16422, "LastWriteTime": "2025-08-13T05:32:29.9001246+00:00"}, "H44Wn7KomEcY+lQZJwOnfGGz2Zr6fOKv1OJQ7gewQ7A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\w8urpsu9uj-{0}-l2lanjn1yy-l2lanjn1yy.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Hosting.Abstractions#[.{fingerprint=l2lanjn1yy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Hosting.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u51ovg8tr0", "Integrity": "HmU5oQiDgAmoKhVmTPb98ITeNwg+5PeO0V6PqeabLuo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Hosting.Abstractions.wasm", "FileLength": 15448, "LastWriteTime": "2025-08-13T05:32:29.8731248+00:00"}, "SSABqBE0i+jXWdbsBidCBwHd+9wRnbHZgb3wg9ILock=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\uvgjtsjkr8-{0}-1gmjxv0m8c-1gmjxv0m8c.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Http#[.{fingerprint=1gmjxv0m8c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wcwsikvj79", "Integrity": "MJscU49SgkOIRHiHFmbUovlPmZyJe4NPJFPlomQEgK4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Http.wasm", "FileLength": 33645, "LastWriteTime": "2025-08-13T05:32:29.8831241+00:00"}, "W1NQMbK6zJkbe35/tFU8Xb+MQyC94G0ZFMkt6hvRDGw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\qk64t4reoz-{0}-ul0xzjnwdm-ul0xzjnwdm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging#[.{fingerprint=ul0xzjnwdm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "94izmhvvny", "Integrity": "ebCNdlTO4dYFeZ4XmQTtTTH8IBgEwAGn1UY9W69jRXQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 18938, "LastWriteTime": "2025-08-13T05:32:29.8921247+00:00"}, "KgG2pvn0pp9XFPN1H/fqyQ1G1bzsRoIYDIcq+w2G+GI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\bsw5ivi4xo-{0}-nwxyu3e2hm-nwxyu3e2hm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions#[.{fingerprint=nwxyu3e2hm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zd0w7usuiu", "Integrity": "CVdLNG4s96Guh/RQeSdL5V//VcDth8QDYPT1iXj3AJU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 24537, "LastWriteTime": "2025-08-13T05:32:29.8981261+00:00"}, "rve0N0yg7/JwjOO6uswABddIjmPscOZsgzNKLSWkSUY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\wi6pqzflbl-{0}-l36scmr1xu-l36scmr1xu.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options#[.{fingerprint=l36scmr1xu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdndjvgz3w", "Integrity": "l0axP0Jtcq9s7NNOa7gZKZP0HuTGb3hAtiGMeJGlSPY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 23736, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "xKTwHOW8jl5MQ/Yo7NLHL9XkvNUok4JMZgDy88a8jxc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\g7endshhvm-{0}-zrlhdwvckr-zrlhdwvckr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.ConfigurationExtensions#[.{fingerprint=zrlhdwvckr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.ConfigurationExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eyji1qzr2q", "Integrity": "epF63jDsOdnAKgocsFzc8hDwhreQ2DeXckhVDcxF3PI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.ConfigurationExtensions.wasm", "FileLength": 5446, "LastWriteTime": "2025-08-13T05:32:29.9031254+00:00"}, "PnXEYMLtmqFN1GB52JBwkrRMKE8r7OEPVURAT6HOjvY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\bg9ysvo09w-{0}-358c2dzezi-358c2dzezi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives#[.{fingerprint=358c2dzezi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rwvm9cincv", "Integrity": "9Xua7toR182nfeb/sTiYATwjO677ueIPZJuTadK38J4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 15236, "LastWriteTime": "2025-08-13T05:32:29.8731248+00:00"}, "IsyT305m2mlcPxcrDH9U6lzOJxL5TSMs+vMsjks3xgs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\81tklvsd2q-{0}-ybaiolflut-ybaiolflut.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components#[.{fingerprint=ybaiolflut}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7j3vzrmjjr", "Integrity": "XW3AWShm12SMmT2UV3r5lfSSO+0zJmAxAc7lfgHj2TE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.wasm", "FileLength": 745840, "LastWriteTime": "2025-08-13T05:32:29.934125+00:00"}, "KeGnJOjSuNKfRI7Apk1xcQUVB2rJLLWxocJR6mc/AMw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\57o2qceic4-{0}-zq9m49vz6p-zq9m49vz6p.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons.Color#[.{fingerprint=zq9m49vz6p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Color.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fq3xlhqb64", "Integrity": "Jwphg4gMA98zDkgg+JAqo0KvKgB0iWZp87bsG4IeP4c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Color.wasm", "FileLength": 359867, "LastWriteTime": "2025-08-13T05:32:29.9861238+00:00"}, "SUGr96ZslN/CN2HlO5jRqOAvAMzjFfGSMBrPv9ofuTw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\imeimuvg47-{0}-xu69rm84hm-xu69rm84hm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons.Filled#[.{fingerprint=xu69rm84hm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Filled.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ze69jqdqu8", "Integrity": "u3OWOx7e9WwUYwTmK8ofGdArMLABeb9goZtPo9/jAfs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Filled.wasm", "FileLength": 1509606, "LastWriteTime": "2025-08-13T05:32:30.1451237+00:00"}, "kXNntqH1MnlR7WJycsWNtoqqk1radTUu4RW7uYld1oI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\nq3jsz6th4-{0}-0e9mwdlr03-0e9mwdlr03.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons.Light#[.{fingerprint=0e9mwdlr03}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Light.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o9htacdrer", "Integrity": "rIYjmC+4nzZ0m4WfFm+Msq3ESrHqjtcCusLKeB3Iupo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Light.wasm", "FileLength": 51628, "LastWriteTime": "2025-08-13T05:32:30.1521238+00:00"}, "5id0QEghPa6herr0vZ2SPXR5hMr1FtTazLG+sPwVyJU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\15xunyunqk-{0}-4xozf64wa9-4xozf64wa9.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons.Regular#[.{fingerprint=4xozf64wa9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m11349pjq4", "Integrity": "CsDphMhUp7MlbPLsqveJ3YJXMCO1aTktLtmADhmUBRY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.Regular.wasm", "FileLength": 1713828, "LastWriteTime": "2025-08-13T05:32:30.4321241+00:00"}, "E4eEpI/8rMbtxXBaBZOyL2KXMZ4B9fKinu7FA4Q+YY8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\yspzdyt2qe-{0}-73zzoh8l6n-73zzoh8l6n.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.FluentUI.AspNetCore.Components.Icons#[.{fingerprint=73zzoh8l6n}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l9yf8r5b9g", "Integrity": "x0CH3qjTU7qVahEdPyva6Ce8s0qv6umX9Kka4QhF5fM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.FluentUI.AspNetCore.Components.Icons.wasm", "FileLength": 10066, "LastWriteTime": "2025-08-13T05:32:29.8721247+00:00"}, "wPf50sGzbsUr8khd5PL9WqHRxW/+md3kqROar7lJrCE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\qs7ozt69b3-{0}-nanjlpvyw1-nanjlpvyw1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop#[.{fingerprint=nanjlpvyw1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7qcj5gomn8", "Integrity": "LyxS4shThCVKPTN9Y4FuUKYqLO+W1PQDpSB9TVPMIE0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 23587, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "Msg6dRgGitE5Zoziacy+xU53rMGq3bMyC0X6XYaYIpI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ya4bywbml6-{0}-btoflm7i7s-btoflm7i7s.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly#[.{fingerprint=btoflm7i7s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qi015hggae", "Integrity": "Hj6zZfJGvxm2jYwZRKFOax/wwnOlOmm9TMZETB5pJ1M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 5722, "LastWriteTime": "2025-08-13T05:32:29.8851242+00:00"}, "2gY2/1qI6wiNEsKFl20vCGe9zf7MR74+suvWcqnV8G0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\xozrar89xd-{0}-9gws8s7zmg-9gws8s7zmg.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp#[.{fingerprint=9gws8s7zmg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "py6av3y9fm", "Integrity": "P/M2iqA6lHfoDf2g5SbIpQDdaLOVaDR/q2/oW7MhNnQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 130649, "LastWriteTime": "2025-08-13T05:32:29.9061244+00:00"}, "HH4cg0dMKl9OF5fnU2G/gkWecl4/B7wR9Dz2LciWUHI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\p6p24vxflc-{0}-hev5t09xbg-hev5t09xbg.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core#[.{fingerprint=hev5t09xbg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4n9eg7mvjz", "Integrity": "sCtt4cqInuOPBk6odutMf3VkVJADHY+0shkNhGlbOOs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 167038, "LastWriteTime": "2025-08-13T05:32:29.9251243+00:00"}, "7qRl01rl3ShYRW80vpzRCBGRn+sQ1bFzSJmYi9lMzRU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\e58q73oxjr-{0}-wy3cb00pkv-wy3cb00pkv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic#[.{fingerprint=wy3cb00pkv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "scj8o8xt75", "Integrity": "/JtLDfFPSgByHCP3+RQj6m3bTzQex2D1f3gQLqqJseM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2847, "LastWriteTime": "2025-08-13T05:32:29.9261241+00:00"}, "VxlbjdxWpGn6IIx+d81ZwXEGDx+jDemVqadpCZsiPz8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\i8zzwev4t1-{0}-nt18748s0w-nt18748s0w.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives#[.{fingerprint=nt18748s0w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4hb8majtfr", "Integrity": "6GWFM/FCdveAb+NLIBiv5CHcQo+TlKoo8kIC4cgRY9w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2183, "LastWriteTime": "2025-08-13T05:32:29.8661247+00:00"}, "w4f+EPL1AnsQHZqbMgKGlIzLJgcZvZRl8umoRE7DsPM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\96uzfo3199-{0}-ykr6iyjchr-ykr6iyjchr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry#[.{fingerprint=ykr6iyjchr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jz3dn1w02f", "Integrity": "kY8OYpvuLwupBxluaednsL+DUmA/TjvP7QPVD4eHPD0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 9076, "LastWriteTime": "2025-08-13T05:32:29.8761246+00:00"}, "9n1IDvVzlDJg7EjG2etRlLpVqM51JgJ+fJ4SHBJ5msk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\nhainmd5cd-{0}-3h1likbfvx-3h1likbfvx.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext#[.{fingerprint=3h1likbfvx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "an<PERSON><PERSON><PERSON>", "Integrity": "9pJkqHFBC2NY/aEn4xr/olsZbQq3qUnBhmMHRafIAf0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2084, "LastWriteTime": "2025-08-13T05:32:29.8801247+00:00"}, "kykCokObg0rl67KCG6srVHA8UdSeds3z67UJf2oZeU4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\tc9i9jtvbq-{0}-wt7n1r1ovk-wt7n1r1ovk.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers#[.{fingerprint=wt7n1r1ovk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bt8vkjq4tj", "Integrity": "oWFVFy9VEWrFIngDS1BcQ5N8RDNsOl7g9VJtwDnlheg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2096, "LastWriteTime": "2025-08-13T05:32:29.8831241+00:00"}, "M+ux8LJol/bYB3BHjzjOtOGu9rqrxEemVEE1/AsrRpw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\f9tmvsja4l-{0}-65adg6natn-65adg6natn.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent#[.{fingerprint=65adg6natn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kunn69262t", "Integrity": "fAJk4HUzbhFGPpIdS1vVKLO1B+m6dmwrIQ+WJHAi55I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 33892, "LastWriteTime": "2025-08-13T05:32:29.8941253+00:00"}, "9Sx/4FB2cELiMtHvEGzspzyDMI6qFTiMBreZBEyWO18=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\3v672o85uk-{0}-dufaq3kp3z-dufaq3kp3z.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable#[.{fingerprint=dufaq3kp3z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pchm9zgosc", "Integrity": "p+kB8Ej93b/+yaTVL73H2ABvgrOzPSglfNIOn+U7jDk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 99000, "LastWriteTime": "2025-08-13T05:32:29.9071244+00:00"}, "5Z1PGtMQ9g1v1DuHIZ1MQGe+rA/s8FqBibCHdReirTs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\q9m82irh78-{0}-rxjrzzpp9g-rxjrzzpp9g.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric#[.{fingerprint=rxjrzzpp9g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i271zrfw6o", "Integrity": "Chlh7NJIKsZVEQEi3mauF4K7+Ag2XQEYrGbmXmSDsdg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14669, "LastWriteTime": "2025-08-13T05:32:29.8681249+00:00"}, "0woO01+pp5uVfmyXwhcyYj9DGWeM+HB8AMJa/TYfNgQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\64zsjbhmm7-{0}-grj2h3kseq-grj2h3kseq.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized#[.{fingerprint=grj2h3kseq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z0w83r6ocq", "Integrity": "I+NWd+jUUXMF+q520KQgVQTuj9ccGxGodS+E9J6qjGI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16270, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "iIZvR8BPhIm1k3lWMLOngHmBHedZ8SzXUEob/qtcs6U=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vvtebh6tj2-{0}-cip8dbnu43-cip8dbnu43.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections#[.{fingerprint=cip8dbnu43}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hm3usfz5bs", "Integrity": "J+orUIlPbFK1X2/dr1RiCbxZNkeLvU/X8xzEZEJd5yg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 48805, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "qjlqfroNOgBeYV/66ilZ0KeXn2KEwtXiUWN6jlI0sSA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\w76y78gkia-{0}-6hr3q9fx89-6hr3q9fx89.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations#[.{fingerprint=6hr3q9fx89}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cpb0eng3kn", "Integrity": "wHqo8h6XLUzu1hRcoNP4f2ZEjdNbX+NRlDyH1hZ04Ps=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 35435, "LastWriteTime": "2025-08-13T05:32:29.8981261+00:00"}, "GLf6mudFyhXqBhoILz3Qiti6nFe4/Ta7CHmggIdGtB0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\psgdso1e0a-{0}-k6p4pn9w0l-k6p4pn9w0l.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations#[.{fingerprint=k6p4pn9w0l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fvul1sxofg", "Integrity": "dc+WIskOQ372c3WH8mrVXoVnq4W6hiLV+q+J9Vwy78w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2559, "LastWriteTime": "2025-08-13T05:32:29.9001246+00:00"}, "VC03LisGpNZcQ83U/wcgCGuvkXnKDBNeEPtWoOejkvQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\kihaaekz39-{0}-p61cj2koso-p61cj2koso.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync#[.{fingerprint=p61cj2koso}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l4kytqk22c", "Integrity": "m8Af4NxHofGqCGWp7u5rkm6N+mJwMvpdOZpScNAf8SM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6750, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "ypJST/lWB/aOh2z1eRA6py605gj34iMjYNL3TJWwims=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\6z5sdd8gxu-{0}-fea7hw9xtf-fea7hw9xtf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives#[.{fingerprint=fea7hw9xtf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3ndiyhvslu", "Integrity": "j0to8pJo9u8tYkxI2jJ3m9BXh0z1bAu7X5JyaW1T3e0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13328, "LastWriteTime": "2025-08-13T05:32:29.8681249+00:00"}, "E3kZxa1OKbNuYx/i0hkFIodKcbngFrQM7WyGYNxHOhw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\aygdka5une-{0}-etd3dkcep2-etd3dkcep2.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter#[.{fingerprint=etd3dkcep2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cx6fwic2qh", "Integrity": "0LjdKFSQvLg6/j5WGDS0AVy+InMoaw7mIUL4gc6q54E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 122325, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "gNhJsED23/h8ObAHfAAAhNuCgZsUPMyZDDi2/c9PR/M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\z921vqe0jd-{0}-0lm42x51au-0lm42x51au.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel#[.{fingerprint=0lm42x51au}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "71y2xfbtch", "Integrity": "y2XCeq7N7+8MX7zi/u+BVNUVi1pQPxbtnO45sdbxZiU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2539, "LastWriteTime": "2025-08-13T05:32:29.8951254+00:00"}, "QJxqNdMsBphDLdoq8QcAgnxCezsztr1GsVPhHL2kizY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\anj21dks3r-{0}-ex6vy58iyk-ex6vy58iyk.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration#[.{fingerprint=ex6vy58iyk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yobrqddsdl", "Integrity": "Dg5UKpAiYzX+NtU/e+RInLbTZXddLrJ71DIYnJWL0TI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3087, "LastWriteTime": "2025-08-13T05:32:29.8971253+00:00"}, "9sN10TZa7hAynwRmGAezn3m83ryPpb77boI7FXgcZT8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ibwgwtd9ml-{0}-s0qgw5psci-s0qgw5psci.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console#[.{fingerprint=s0qgw5psci}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ngepxhj1qb", "Integrity": "dyo3oJVxeBcrGbDhBGaL0dlgeZDk26tAe5b9zcQxTjY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19339, "LastWriteTime": "2025-08-13T05:32:29.9011249+00:00"}, "pFcGdKy+x0y8JjLN4XOUZyhq/nQN67QQD68duEEMYac=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\aza08xqter-{0}-zknkrutld3-zknkrutld3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core#[.{fingerprint=zknkrutld3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mih6nkjw7s", "Integrity": "0uqLpmtXW9OESWh7rLAUuLs5w4glbETbRIZyeM5jKjY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4532, "LastWriteTime": "2025-08-13T05:32:29.9031254+00:00"}, "X6gWk0JLBwqCpQDDtG7f67vWgA4MPICQqS+IUc5a9Ds=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\m7svytl5ks-{0}-lu92ceoi50-lu92ceoi50.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common#[.{fingerprint=lu92ceoi50}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x8metbvzuj", "Integrity": "356Jla1j06TcAqnuDdiLYf3IG7unNcFM9JLNeiX9wos=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 374764, "LastWriteTime": "2025-08-13T05:32:29.9131238+00:00"}, "rzlYZ+/XuW0+ThNw+zeLJO+Ustd3AC12wyuSk/6nUus=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\elhy7bq5p6-{0}-2ddk0zm05l-2ddk0zm05l.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions#[.{fingerprint=2ddk0zm05l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xl098vkyuq", "Integrity": "q/AaebkCc/DLJV2T7GFR++Mw1ItvEVO6PBQjkat3AxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2050, "LastWriteTime": "2025-08-13T05:32:29.9001246+00:00"}, "75m7TDYTYDPC1/dbZeV0GE4ZYrJuVnS1W1YDhN5ZRw8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\57xckq2kuw-{0}-3adg3wr0gn-3adg3wr0gn.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data#[.{fingerprint=3adg3wr0gn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rwkch9v50u", "Integrity": "JWbhFXCOjNHnf1DAX/noR7KB5wgX8nwvLhll1Bck+nQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 4984, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "yLbzcQaxC9aK5trlsuobMN2n6ncZ5TrwphX62zr+5WY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\5ahhucdyag-{0}-voyqcmzm7a-voyqcmzm7a.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts#[.{fingerprint=voyqcmzm7a}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c5gdiqwu0a", "Integrity": "AA1RwXPVK9QeXboIUu8X3IAYo04HSJbjRWMRuUqEmYg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2368, "LastWriteTime": "2025-08-13T05:32:29.9031254+00:00"}, "l0yG70wLBBRDe/N4JIleyFVfS/R9ma3ELwUEasvKpLE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ojpse6rttu-{0}-tuw7jnpdtf-tuw7jnpdtf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug#[.{fingerprint=tuw7jnpdtf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "53sdlxfho9", "Integrity": "j6dsIWzaW4b1wty9nz0AXtQ+7l7BJJJjVMAFP/0l3N0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2261, "LastWriteTime": "2025-08-13T05:32:29.9041247+00:00"}, "JValv674CQICaWQfOsDjei8CdrWovBxMb4Sg1WUbF3A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\1aorrtblg4-{0}-orwvw7tsnw-orwvw7tsnw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource#[.{fingerprint=orwvw7tsnw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "430k3izu7n", "Integrity": "kKenqNSG1mn8Q5JMpJalOLMGRXLViALWmXBV4fHYzeM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 73039, "LastWriteTime": "2025-08-13T05:32:29.9091244+00:00"}, "PhJQCuHhOtyUE/bEB/BjQU/eItOeuAA9/MFiqpGFzRA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\r0w0ototjt-{0}-i2nxqnh8ia-i2nxqnh8ia.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo#[.{fingerprint=i2nxqnh8ia}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n56t3u4fyr", "Integrity": "lSBfrJW/gEWdDE3RMoqQWXw4SYAnTe17mOXKQGU3CQk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 5092, "LastWriteTime": "2025-08-13T05:32:29.8661247+00:00"}, "AtidhynyE/+bJL8rwrfP9pFCIJdRn2H0JXbgLBqTmkY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\bip3i01j7r-{0}-yj1m2auw1z-yj1m2auw1z.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process#[.{fingerprint=yj1m2auw1z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9nbpdmtg7q", "Integrity": "NRxRUbdikQNgM8zefCj20EhyRb480SDvHScGJxHLKoA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 16123, "LastWriteTime": "2025-08-13T05:32:29.8791247+00:00"}, "OV8gdPhrO60LaNAdtTCIUwfMMChzrvM8eMeUAgy4+SQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\jox0l4vbbb-{0}-9u6hm41m9t-9u6hm41m9t.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace#[.{fingerprint=9u6hm41m9t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fyp1qsyfel", "Integrity": "WZwiJb/2SorUA/OpVYbO1STlPG/liqyZxkLdoXfR0mE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7333, "LastWriteTime": "2025-08-13T05:32:29.8821247+00:00"}, "AnLmavUF5z2ujI+d2YKWV2SnfLTTfSMnIgUSCU3zUfU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\dc5r2mw9wy-{0}-670flx7nki-670flx7nki.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener#[.{fingerprint=670flx7nki}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gj7ifh3xaa", "Integrity": "VEbIw32HqPniuSbsXOgnb/+Prsfn49ZH4xHauexX7N4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9342, "LastWriteTime": "2025-08-13T05:32:29.8861239+00:00"}, "xle7Cy0QfzH0XhbX2Wypphai93L3ulqYIzA7Ul871aY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\2r5l6t1go0-{0}-2vqkac8ysr-2vqkac8ysr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools#[.{fingerprint=2vqkac8ysr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nwk46c1t8f", "Integrity": "1witXnaOtAks7FvyUm2StvA0vRUBxsE2u6TuFk5fU08=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2163, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "OynJtTiPN1rCLqmqtZHmGi87kqzbHNJm//I4nWZZhrc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\l6ybehwu6y-{0}-n515vmkk2p-n515vmkk2p.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource#[.{fingerprint=n515vmkk2p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s<PERSON><PERSON><PERSON><PERSON>en", "Integrity": "IKuf1JVmbIDiPeKEl9n7d0wYw7Xl4wXDYy04p64VKQ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 20045, "LastWriteTime": "2025-08-13T05:32:29.8971253+00:00"}, "kvflI30o9uf5E5M6hEhHyZoRmcMIWdtmtVV6DSsqXWw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\nzfl03dw7f-{0}-ogliygwa1r-ogliygwa1r.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing#[.{fingerprint=ogliygwa1r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j2jkknxc2z", "Integrity": "i/3tOGuWeSBAFf8YtAMjFoXt3KIVu/xhuBnA/uJq0Q8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2485, "LastWriteTime": "2025-08-13T05:32:29.8661247+00:00"}, "EdbUnh3npZvsSq+y9jX6bZ8YtXh02KNXgbdWCxABp8g=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\43fmqoax5y-{0}-zk693pwck8-zk693pwck8.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives#[.{fingerprint=zk693pwck8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cj17u8p2wn", "Integrity": "iBE86Bo/43dcRhAbRDPII6PGWltu7+o1hrrcsSmm/LQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 24006, "LastWriteTime": "2025-08-13T05:32:29.8791247+00:00"}, "PYI0uc9ttR/YOc2H9Rj+WoTPPnkjuvXlpffhRnUNCfs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\8bbl0axy88-{0}-wxhr0xa5hb-wxhr0xa5hb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing#[.{fingerprint=wxhr0xa5hb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ojg7c2wbp", "Integrity": "ZwGMejrYM837zRUjYQPTPYYPbLXhh1ZwyRdLGtLngRg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3830, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "GC+2N23nGaKG8GZ/Bjo59iGjD5zMe+Fto2nJp/ekahs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\fgep7yzgsk-{0}-ipprcrczgj-ipprcrczgj.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime#[.{fingerprint=ipprcrczgj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lyxfv904ef", "Integrity": "rZzYrBJQvfgabnvslp2rr1Ylwc4Q3JLc3LAhNjBQacE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2422, "LastWriteTime": "2025-08-13T05:32:29.8851242+00:00"}, "32+mvPCUmvF9Se/gM+SIMXAnlN43HOYUqpX8CxzgnIg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\z1n8pzvswm-{0}-okhe897m5z-okhe897m5z.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1#[.{fingerprint=okhe897m5z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "thw1m5qtlc", "Integrity": "P7tL44kDzlbU+9paVbZGTbXA94RkvbSYvmiRFEaMHhc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35105, "LastWriteTime": "2025-08-13T05:32:29.8941253+00:00"}, "45HlwefJOuh5LqjWcuoJVDRGMYSXKxPfK9qyeknH+Ao=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\9nmsy0qx69-{0}-i93u5bq4fn-i93u5bq4fn.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar#[.{fingerprint=i93u5bq4fn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6hm7p40dba", "Integrity": "dgkjBRdk5rLU0TgdsDKpupLea+4/L7Aq29DddzUEeCg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 10373, "LastWriteTime": "2025-08-13T05:32:29.8981261+00:00"}, "sfhV8x4iQlAGrXS0lihnihiV4fUZDNMvcpQaCCHV/BQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\dwo0a7f9mx-{0}-x0sb683rhi-x0sb683rhi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars#[.{fingerprint=x0sb683rhi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "006wanlgvt", "Integrity": "oMOAdO3ne7ePS9Fv4zAk4AeFSgXe9ANkS/8h7OeH4Zo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2276, "LastWriteTime": "2025-08-13T05:32:29.8661247+00:00"}, "J2lXj1C0VT3aSxfZQz79NK7f6y4InMIKNrmaGLgPuj8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\jzp1wb8xyi-{0}-o54lsqobzb-o54lsqobzb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions#[.{fingerprint=o54lsqobzb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ck0h74m2d", "Integrity": "91N6AqYjJHpnr7J/39CPAgxi7wXw16tPJihPAfZKUU0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2159, "LastWriteTime": "2025-08-13T05:32:29.8771242+00:00"}, "7prl076/8gxnfm52U3ZE8gBzyUza1nO5208z68lbI1o=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\xjww2bjgnc-{0}-tde8zuw0yw-tde8zuw0yw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization#[.{fingerprint=tde8zuw0yw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jccpixzi4o", "Integrity": "Pc+9uh03752/oPPuV4joxzNE25G8aI4T20zs3/NA/0s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2242, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "vbJJF05uYYihHxCA4kD0/Oasa7T+QrTEkYVs8e5z3mE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\xb1kei323p-{0}-vx3bcge4ol-vx3bcge4ol.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli#[.{fingerprint=vx3bcge4ol}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "opw8fc0znb", "Integrity": "ZMnDiZaE1Q6nnHQHoDgVGCSiP4gOGtpmcWdEItLyE6Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 6948, "LastWriteTime": "2025-08-13T05:32:29.8851242+00:00"}, "jejzVL9jTZst8Npdb9zn4o2MTiRIPMbibec8oOji1/A=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\e8rw8mymn3-{0}-yhtj6e0w69-yhtj6e0w69.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem#[.{fingerprint=yhtj6e0w69}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q20yo0ya1x", "Integrity": "HhOdcMKOtx/ua27Bf3+EnP6NPIiAT1jfYyztY4//6AQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1978, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "Rciu4T7DMx5I2Z+2pIjjSZZ2AdkWb+cG0QEjsOeILyI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\poputdfpjz-{0}-quahjtap8r-quahjtap8r.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile#[.{fingerprint=quahjtap8r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5nqh9yupva", "Integrity": "382jMaKFpILh4xIse+h0vgCZAY7c92UPOJYNGqU4To4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12398, "LastWriteTime": "2025-08-13T05:32:29.8951254+00:00"}, "EMYYBOfY+z626L9z6Or4FHCBcFI2Nyi3HTzN03fKasY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\7l0ju3idqo-{0}-jtaurxkbzi-jtaurxkbzi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression#[.{fingerprint=jtaurxkbzi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "juehst4pau", "Integrity": "leqO8cTmEDWVYT9pa+fvtLvjVhAtR17wCHfcNNdJjPQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 42932, "LastWriteTime": "2025-08-13T05:32:29.8721247+00:00"}, "Fdb6zbCxCM3khmSRW8TiVE1qA2jZsbXrtlxsP5ndIkQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\yu90cxj4sc-{0}-3yfpgyrku1-3yfpgyrku1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl#[.{fingerprint=3yfpgyrku1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "YtMMthf+4HE+hn/h2M3PKGA7sH1HnD7ddo2xd384vLI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 8451, "LastWriteTime": "2025-08-13T05:32:29.8801247+00:00"}, "OrjxlOyfGvv+2yy3UnN3OOlISuEk5tY+XbNoSWTBqSg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ffzspv67vf-{0}-8nnv647ull-8nnv647ull.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo#[.{fingerprint=8nnv647ull}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cnjcvox7qa", "Integrity": "eJ05JAMdh501aSSXjS8rraq877jyDneE7vXhwgrQgiM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 6000, "LastWriteTime": "2025-08-13T05:32:29.8821247+00:00"}, "cvMHg7IGEsNjfCRjtOwpJIHyUQ333wXNHWUNU+QBq1Y=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\roxlzif3uz-{0}-ir5j8vbyan-ir5j8vbyan.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives#[.{fingerprint=ir5j8vbyan}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lxessetpsk", "Integrity": "ZyGESdx2tAK7N0v+vHnQdwmWVg+NOZ57ersqZp78WU8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2162, "LastWriteTime": "2025-08-13T05:32:29.8861239+00:00"}, "KKelSrhnsOAebbmBphqPOuqDXs9iyoX9HhdRoE4bff8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\zwge3ab66q-{0}-1lxrwwxsho-1lxrwwxsho.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher#[.{fingerprint=1lxrwwxsho}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "quqljnapdd", "Integrity": "zvWoc0bVL231LCjk/fW+Q4Jvi9HEqvV2pyv4ocUI6u4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8729, "LastWriteTime": "2025-08-13T05:32:29.8921247+00:00"}, "PdxfLCHyp1gFlvJWtOnG/I66wI+U7f7smPiSlI7IsB4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\wfi02spp1l-{0}-gyxexdekj3-gyxexdekj3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem#[.{fingerprint=gyxexdekj3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rxe2iyf0id", "Integrity": "H8KQtGc1WhIMYjTCBmykIoaIKMBjl1xDciaxSL+kPss=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2280, "LastWriteTime": "2025-08-13T05:32:29.8951254+00:00"}, "wU1DWRaqASddTdyy21EPbSvX9wmGnIMpqyvTkqJXwh8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\fo4i1i3i4a-{0}-tsgf6g1ztd-tsgf6g1ztd.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage#[.{fingerprint=tsgf6g1ztd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9z8y2lbose", "Integrity": "9ceafAgJmEgeh4MsSrbsK2HbOcDY+Gdiad50w1fiyuE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 9327, "LastWriteTime": "2025-08-13T05:32:29.869125+00:00"}, "YtCcqLC/s83o/Wtyt4NrlC9mvvuna3sWTjRGhMjNFXs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\jl2lh0k83l-{0}-j4sjofqyi5-j4sjofqyi5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles#[.{fingerprint=j4sjofqyi5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0ipxlufb5e", "Integrity": "WBw9ULybZN1IqkgBL8CHqMekj5q9PDjtIRHKbyj4owc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16563, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "8PusHNK3RNCfLR1qEJUiwpRSxcBYKia3JLEo2s1tops=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\2dwksosxii-{0}-jiaey0kmyh-jiaey0kmyh.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines#[.{fingerprint=jiaey0kmyh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gijixpgft1", "Integrity": "uEczn81KUbDuKrP7jMv13I1irPYEDtuEn0NcUij6zhc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 30345, "LastWriteTime": "2025-08-13T05:32:29.8871242+00:00"}, "5PkCOZ2Z6f6W66xkHErrGtg+btU3gXRuOoo9Kp5r3PU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\l0llh7kyz2-{0}-qfh40ih8l6-qfh40ih8l6.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl#[.{fingerprint=qfh40ih8l6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3sux2pq4wp", "Integrity": "7f9FORmze5+pA2jlHegTfQhmg3MMkBi6OdgT+hgJ1dk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5543, "LastWriteTime": "2025-08-13T05:32:29.8921247+00:00"}, "bYlYARxgcGI82uWVgvilNpPoQBgAuwNuAhFi7NZtjfI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\beke1dme0m-{0}-al6w1uowde-al6w1uowde.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes#[.{fingerprint=al6w1uowde}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "agd<PERSON>uwupr", "Integrity": "5tnvU6xyOtLzTdutD81vkRRRmf3Jh2jiZmyYflvJNd8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 11358, "LastWriteTime": "2025-08-13T05:32:29.8961256+00:00"}, "2NQfMHtNdv5wOJBvKapAbdwvticgmxVYdw3sb1pWONI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\x74whs6c0q-{0}-d0g45p3x9u-d0g45p3x9u.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream#[.{fingerprint=d0g45p3x9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glpr2aie2i", "Integrity": "rPsdyFmgq0/imC34GoiqGD3hseMx0FQta0LooHYaE9g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2190, "LastWriteTime": "2025-08-13T05:32:29.899125+00:00"}, "A7Gz4zBdnXreT6xF7l4keANl5KbbdK5hlgYNZnduBok=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\50z04zqcpo-{0}-2zge8rv4ra-2zge8rv4ra.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO#[.{fingerprint=2zge8rv4ra}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wp5isv3z02", "Integrity": "dFXM/nIZknEZVvInbxuL1HuTkIbOiXkLqeURsI8gyoA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2238, "LastWriteTime": "2025-08-13T05:32:29.869125+00:00"}, "lKryBiI+wmTDmy0jNPX1D4eo0jOqlMeLW8pA5EZxpuA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\x16kq5oj17-{0}-2lw1u6ymmp-2lw1u6ymmp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions#[.{fingerprint=2lw1u6ymmp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xg6l0covpt", "Integrity": "AQQPRB3GgdAO2CswyzjMZd+jKiVrotxXuLGwzTL6by4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 213260, "LastWriteTime": "2025-08-13T05:32:29.9041247+00:00"}, "wpUtH5HyhelRsPLJw6vDQl8Srai2g3jOSxfxO+Is9yI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\bveb3z22ja-{0}-1jvfownmci-1jvfownmci.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel#[.{fingerprint=1jvfownmci}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilvxh1ytwv", "Integrity": "NtNPCSyHCN574iDUPgfqTV7YFp8/KGUgkNDIQg6n28Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 86356, "LastWriteTime": "2025-08-13T05:32:29.911124+00:00"}, "uz/N/DWAQIFkHYawXIKH5hqWkOyFMAn7HWBBukQVDWk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\o0ba4eoqgp-{0}-mv4cb7fqwu-mv4cb7fqwu.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable#[.{fingerprint=mv4cb7fqwu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uv<PERSON><PERSON>ufq", "Integrity": "Riz4tUKdY++aWBxq26g+aMuOXBzbcfyphtldt3hyy5Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 20738, "LastWriteTime": "2025-08-13T05:32:29.9011249+00:00"}, "e70it1YeE7LIiaa5/ZUljRIYIz2v8r/XJFFqKn8STLE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\rl8avrva5j-{0}-3djr1lshgb-3djr1lshgb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq#[.{fingerprint=3djr1lshgb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "smp0gt56xk", "Integrity": "PArPaNRR05jB+JDVm7ielqd774A1A1POge6nxh/VMTU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 55271, "LastWriteTime": "2025-08-13T05:32:29.9081247+00:00"}, "PMB1QTzA1u4jmKBpmaWxk6SaWsy8H64sgMhQ4QKyqbk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ez03ttwnqe-{0}-ub9sra6ubv-ub9sra6ubv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory#[.{fingerprint=ub9sra6ubv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d6altbm6d5", "Integrity": "xU8tNgjVOF9bmHJRcuU6IDJbVwNjR8oat5Ybd3qY7G0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 20547, "LastWriteTime": "2025-08-13T05:32:29.910124+00:00"}, "wfKHCHEc7cclnCAOsiHGnbQ+5uZ5ck+iKmiawgjFw6g=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\jq2h3qwo4i-{0}-346n69ja1w-346n69ja1w.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json#[.{fingerprint=346n69ja1w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i5psbi7mvj", "Integrity": "VtG7QnIonM8hzBGyByHk2HTZemu0OmnOShvdsgpj9Zw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19490, "LastWriteTime": "2025-08-13T05:32:29.8711256+00:00"}, "iKYXPQihldRv0FYh/Mpi5Rh3wAuoitgV+CoUPvaXyas=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\45is43ruwj-{0}-eupgag7vx5-eupgag7vx5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http#[.{fingerprint=eupgag7vx5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "loiqkrmtna", "Integrity": "654FulOg9qnc9OSAZP/125wGc0m4sfwcxNctStAKx7I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 113536, "LastWriteTime": "2025-08-13T05:32:29.8941253+00:00"}, "gDXqJGeXj4A79/sjGQlrxXhw8OV2f0KOZQYTfpCYtX8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\pcwospe1lp-{0}-m0tberhw26-m0tberhw26.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener#[.{fingerprint=m0tberhw26}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ja364f2x5e", "Integrity": "1Snk6wdtqtHGmagUcXRhpKWIFVkqiOR48TvtrClqWBc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 16009, "LastWriteTime": "2025-08-13T05:32:29.8981261+00:00"}, "W20yQYpdzdAVTFqvmG5nUDICfX/dEJt4Ke6ifDmCv/I=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\7ti5h58rif-{0}-7wmkfq1voo-7wmkfq1voo.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail#[.{fingerprint=7wmkfq1voo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wx1bfs34zk", "Integrity": "bJLUuUtzJSjaXJGnRL5u/FEVIQ6EWDxxF6kdv81AwvY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 41477, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "IH9EGHgTwmDYnFkx+tR/vqFsWU2DFYxC0iNK3lBZFyc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\82r1g4we6k-{0}-ee8vwc4vcc-ee8vwc4vcc.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution#[.{fingerprint=ee8vwc4vcc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4vetenh963", "Integrity": "gMpSf1orS+hph1sOgVN1UaGGYrgX/WFEoUoph9UGvpw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5907, "LastWriteTime": "2025-08-13T05:32:29.9031254+00:00"}, "lDy6YMi7psgBIKyDwmxi0wpgczuBmMbbO78W5Wsuv5Q=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\em99c8jnhw-{0}-h1hduhi84u-h1hduhi84u.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation#[.{fingerprint=h1hduhi84u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sbp02d7as4", "Integrity": "OAby/HFG9vc+4B7CtyQBuX7mAy0n8ztmsIZ/Be8yvjs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 12686, "LastWriteTime": "2025-08-13T05:32:29.9051243+00:00"}, "V96tr4aHj5ZojmlBmPRBM7YNo+eKkqXCuNSQWP8/HRk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\h34ni7e34g-{0}-y4g427qvfa-y4g427qvfa.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping#[.{fingerprint=y4g427qvfa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8seju6crsk", "Integrity": "mAewmeGNfUBRukBCYI/914Ci967H5xFJo/Zly4e/KXs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 7503, "LastWriteTime": "2025-08-13T05:32:29.870125+00:00"}, "xCXFyoRqFI+KjPLZPiq2gfwkvgaoyF2P896+lw3jYh8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\94cb79qhd1-{0}-zv1ut64ban-zv1ut64ban.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives#[.{fingerprint=zv1ut64ban}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hqe6mxtelq", "Integrity": "GdpA7250jskYLr90zEBdYe5p2ACS9FXhU4oQUq3K/xk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 45391, "LastWriteTime": "2025-08-13T05:32:29.8821247+00:00"}, "bq6olkjLx6XwMAAokdiDHr2RFgDps3Gz/DjBLyYUNvk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vuxv0oh5pv-{0}-lnozeoe9re-lnozeoe9re.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic#[.{fingerprint=lnozeoe9re}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "58zycxkdqr", "Integrity": "1Rb4jsnZt9p5oM2RJlQJPC1jbMUWA/+tNe7rP5TW2u4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 10836, "LastWriteTime": "2025-08-13T05:32:29.8871242+00:00"}, "qP+mO+0/rx5ucYFgmxpESybcjMcHiE99m7OTp0207so=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\gzsmo1yn5q-{0}-omoxxcqo90-omoxxcqo90.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests#[.{fingerprint=omoxxcqo90}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cqtdci0h40", "Integrity": "xCuzhGRvCzETq68XqAvUbtr+Im9pED0sok0JTeNXHBo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 20253, "LastWriteTime": "2025-08-13T05:32:29.8951254+00:00"}, "b/DJlxO53ETv+KS7y+NwQahlaFI7MUAPciIQQs12GFE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\m932zgjobt-{0}-t3a07csu2b-t3a07csu2b.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security#[.{fingerprint=t3a07csu2b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uqon2mzc8o", "Integrity": "+pjvw3ruCLqHXxCz1zithFOPOPEePFmogvfEk1THHFA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 32524, "LastWriteTime": "2025-08-13T05:32:29.9011249+00:00"}, "Bvdbn68ous5b3S4hnEo1bY/XhNrU9ABJMc5+GjGmcMY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\rq1mtysn1k-{0}-5v95sh5c67-5v95sh5c67.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint#[.{fingerprint=5v95sh5c67}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zlbir1205i", "Integrity": "R8rmpIgNQchP1NMQYAI1rZAoiFpxgxRiqnGXP4+ydwk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 2152, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "8g4QuO5DnLdelcHGJYTFENpPoO6JQppx8G0Iq10cIxE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\s1efqdiea3-{0}-ww3h8yu74p-ww3h8yu74p.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets#[.{fingerprint=ww3h8yu74p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ot9m8bjhum", "Integrity": "PF+o1MbsC+93PkrqPHe8/XKIACXpWHwlDryWZjT3jsk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 22953, "LastWriteTime": "2025-08-13T05:32:29.8711256+00:00"}, "VCNWlule9SDgxPUU/6NsAcldotfDaVv1Ij7RaqG0NC8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\q2jua0tmuz-{0}-345793p9fr-345793p9fr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient#[.{fingerprint=345793p9fr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hqe4lkcfku", "Integrity": "Z1dHxianR7H+OMYXJtqS6JKSvvRghsuuGO0C29ypfoU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 14319, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "EAjABs7qCivI5S0a0wRXjX4A4Zbf9bDwnbq+TA8tIuQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\8kbh7hx785-{0}-odv41wuu54-odv41wuu54.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection#[.{fingerprint=odv41wuu54}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o91o3hwzqk", "Integrity": "lFy+BAfPRtfWFLzJ9YnD1KPq2NpTIgk/ppi38REPvCY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10167, "LastWriteTime": "2025-08-13T05:32:29.8851242+00:00"}, "CYW/aARhYbUfZs+VYhIU9GSl4j+e60rv/ZyJ05gdkNk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\gbcomxyi1p-{0}-ksx7w94zni-ksx7w94zni.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy#[.{fingerprint=ksx7w94zni}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z5q97d6g3f", "Integrity": "SfhttF5640XI6GYrcDGUckL/YdKb6HoyzxzBHCg0ttU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5575, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "p19k5eIr7rXmIi+Urf1iin/H08f7dZ31Y5eyOmZR6Z0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\9jdih4wfh0-{0}-b37svw0y4i-b37svw0y4i.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client#[.{fingerprint=b37svw0y4i}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9czklybx9l", "Integrity": "FyN0F7rfGC/asE4fD25Bdm+Sq5xVFGyn9tSXhLsPpZA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 16984, "LastWriteTime": "2025-08-13T05:32:29.8951254+00:00"}, "UfgG/2PC9q7T8qoeR/KU9L67NREBtG8gTsD6H5/J/PI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\42zck7wz4x-{0}-9fasahbeiq-9fasahbeiq.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets#[.{fingerprint=9fasahbeiq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8rwwylp5qs", "Integrity": "1BMYx1Cw1qIyTsF3vaqVDqHo2EjPclyPY5AZyv7ScI8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 38019, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "m6xi52J6/I7Yh5C26lfbD2/JB5WbMBDi3TRTFy/s5Vg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\483dhelpts-{0}-qt5fpja9tg-qt5fpja9tg.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net#[.{fingerprint=qt5fpja9tg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rqifglrb09", "Integrity": "owAcbjtoQgtT/So4jP87HqATQ9GkhKjCkXwlbYRGA/w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2730, "LastWriteTime": "2025-08-13T05:32:29.870125+00:00"}, "m1CZyA8AsYlG6ZgdEjMrHfcl/GGGoIrbuG6Oe0Z8wo8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\tvgd981dy3-{0}-i6kirq3og4-i6kirq3og4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors#[.{fingerprint=i6kirq3og4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sa1vauehol", "Integrity": "rEVZXdbH5DuhWWmnAuB56nM6edF9/xRBi9ZYjxG3vwI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2237, "LastWriteTime": "2025-08-13T05:32:29.8791247+00:00"}, "9nZnPj0XAM54g2JxMqEfqq9VP9znA4yFwNX72/QJ7p0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\hfdqawm983-{0}-497r8m9pev-497r8m9pev.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics#[.{fingerprint=497r8m9pev}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j8p4o012tj", "Integrity": "wwBPncrNI7MX8kHYaAxMcMZx7ik6e15/4HcAsEtqJaU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2015, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "zf1HpSnujL51rKblZ9KuP2GK8DaJXmywkGshng+Pp58=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vm1gcbf22l-{0}-k9az0iuxjb-k9az0iuxjb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel#[.{fingerprint=k9az0iuxjb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "knujs8pz4y", "Integrity": "8C0YKRnpnnVzih0m2lkzD1kmA7ZHNxDCz/tCHhwLoj4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 13315, "LastWriteTime": "2025-08-13T05:32:29.8851242+00:00"}, "TdCMW8/czHdSqSnQ5wGtpVjAaaNuUVNwvDxLcsVvkR8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\8f35vmpev5-{0}-mnc7tnpegn-mnc7tnpegn.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization#[.{fingerprint=mnc7tnpegn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4nhqyqjd0m", "Integrity": "0FUeOiTPhuwSVz9F3IN29CKXYxDLljnzWVaYWVg1S9k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 299896, "LastWriteTime": "2025-08-13T05:32:29.9231239+00:00"}, "IiQVWJrmcJ4gQbBIaS3Wlt4W2bjCF9srdXUIPGoyowQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\i9sh5r0msv-{0}-58q5onb7r6-58q5onb7r6.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri#[.{fingerprint=58q5onb7r6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pchy46s1xc", "Integrity": "pxdcYQ1DLrLK/h00+LJZMhKB7+EFevsfZTtwDTxXck8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 41377, "LastWriteTime": "2025-08-13T05:32:29.9261241+00:00"}, "n/4penQXFp2P806iLq4uInF5y3DhGHEcI2RRwWmnD6M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\9xu5xe2r9n-{0}-35ud51k85s-35ud51k85s.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq#[.{fingerprint=35ud51k85s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m1rlsy4m60", "Integrity": "KqCyxuZYmsAfwEPPZ0F/488iJkuvZzfOu2eVW/qCExI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 58363, "LastWriteTime": "2025-08-13T05:32:29.8831241+00:00"}, "jO4oeICK36V3GFPpPgPoxqq+HTO56NcHb7zXQFR90Rs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\8xsqevfdzc-{0}-ygkocwikl4-ygkocwikl4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml#[.{fingerprint=ygkocwikl4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvh4j95sn3", "Integrity": "ecUo9oQUoTPdJcPGncPE0msrwGPXxbZkBz5sr3VppjM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1051048, "LastWriteTime": "2025-08-13T05:32:29.9871246+00:00"}, "MUNvANiCLzBQce+hAa6j0bu8k7yX0QkIAeGNpaOtYu4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\sqkutjk3c4-{0}-13d6e679le-13d6e679le.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy#[.{fingerprint=13d6e679le}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o8hjpogzto", "Integrity": "+EjhOpngMTzKRlzuKNVyDMvnxjw059cVF0ukmolE9ds=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 12761, "LastWriteTime": "2025-08-13T05:32:29.9881238+00:00"}, "57Hu2bG1dEQ0uXINuvY18KG0aS1ghfEn20NYmk6Wi+k=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\54cvmzu53d-{0}-tnlqh325q4-tnlqh325q4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration#[.{fingerprint=tnlqh325q4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hho693depw", "Integrity": "i1ngFtiXTy/6FsOQm7c4Azd4zmVw6j3rnEhMf9VJWlE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2252, "LastWriteTime": "2025-08-13T05:32:29.9001246+00:00"}, "EzlhFIbmti3uax8j8TSwN9W2fvBnhqpGuEpPKRGi9pM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\n1kq19s6el-{0}-6pezgz31ve-6pezgz31ve.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight#[.{fingerprint=6pezgz31ve}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fhwej7xl0t", "Integrity": "81qMId0YlE5nbgbSAnq/FkOfMGfzoPDALAg+ttPM93Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2211, "LastWriteTime": "2025-08-13T05:32:29.9011249+00:00"}, "pMk7TuMbIGTz/vF5RmjmMl4xtcft7vQkbKEydGoInC8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\x5hqg6cowv-{0}-1sfjh9emmw-1sfjh9emmw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit#[.{fingerprint=1sfjh9emmw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tiuqkhq61b", "Integrity": "BbVCIMEgnUdXbOg6jwDW1igLbg2QSg+M/3QZ0Uz5uMA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 51910, "LastWriteTime": "2025-08-13T05:32:29.9071244+00:00"}, "E1KLFHe+r/yodn4m2i5DBWc1fAc0xbrHZOfVGLZRRL8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\286qe4bhv1-{0}-o3fapkxyot-o3fapkxyot.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions#[.{fingerprint=o3fapkxyot}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gl3p1r7887", "Integrity": "jMQ10h6dzoeIGnw221ifset/iHvrVSgm6QOMSAiXi98=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2133, "LastWriteTime": "2025-08-13T05:32:29.8721247+00:00"}, "rkDMLGxlN95/3ZO5RaxXwXrCpRLEPO5t9dSeq2GSTQQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\dj0n193gf6-{0}-tx83z6ho7l-tx83z6ho7l.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata#[.{fingerprint=tx83z6ho7l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t3lloxkty0", "Integrity": "5+UdfXW/wvKBdO6HMMV5JoZQoNxsgO1Je2NvHHUhIsM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 191567, "LastWriteTime": "2025-08-13T05:32:29.9011249+00:00"}, "AMr07vfqHtW4z/WS74KUGC/tBQe1Xmi+LxlHuQNQR8Q=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ul1bfq645u-{0}-z6035msxdy-z6035msxdy.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives#[.{fingerprint=z6035msxdy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lakktre51v", "Integrity": "vUzs6DTCtKI+1YzTCKFeig232Mfbf0AC6HDILVBd2+M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2339, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "CZ8wGjLcXi2zLssAXh9r6YxfevJYjDKjvQbLlNlhWjo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\1a6zcvk1ie-{0}-xqbpbwu9vz-xqbpbwu9vz.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions#[.{fingerprint=xqbpbwu9vz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uxz5g5z1gt", "Integrity": "zgYrUuxxIrPjU/BCoRUzQPQNpmywvWT1PC49els68wM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5636, "LastWriteTime": "2025-08-13T05:32:29.9011249+00:00"}, "g9IL5RFmGsZRLJwXT8NYva5LT2B57+0K1uLrOSoarR8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\bpd6squtnx-{0}-1kaq8volf4-1kaq8volf4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection#[.{fingerprint=1kaq8volf4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mb22iyztzc", "Integrity": "QgJIiiQjbh9pNGxH0rlsEMMMlJ2ZLd7O0oI2q87W+B4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2433, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "qvGco6T+CIQyqxdxeAPPuKWwgTegyMJZYNFs7/wa81k=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\tgagesl3sz-{0}-3d1gwadcaj-3d1gwadcaj.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader#[.{fingerprint=3d1gwadcaj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t0azmyai34", "Integrity": "gyTPBHOBz37W84V7sK3jBsrkbMY5B77huOuF7JK5hbg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2102, "LastWriteTime": "2025-08-13T05:32:29.9031254+00:00"}, "tNs5PnYbtGRGnX0qbHD5IKa/imvs5BXJi/2vdR6/II0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\c01xkjda8r-{0}-pdb0cwov9g-pdb0cwov9g.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager#[.{fingerprint=pdb0cwov9g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n7c67ifhnp", "Integrity": "U6KWPy84NlRffTpZ4zs+bxMvPXZZOowqqPmyJ72Cesc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2217, "LastWriteTime": "2025-08-13T05:32:29.8731248+00:00"}, "fjCLghrQR9sGvsmN5kBbDfEFFEYQyWQqCAq4+8ugXYY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\awv4v28y8r-{0}-wfwt17t25p-wfwt17t25p.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer#[.{fingerprint=wfwt17t25p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kklb1i9vmg", "Integrity": "9LqoDHjlXgkCI2Zq7oQMY2qp642ZM7kLJPm4WUM9IfE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7574, "LastWriteTime": "2025-08-13T05:32:29.8801247+00:00"}, "nK7LdJPdStmejkaWYl1LQb784vsdOmmaRoC2P1X4vZk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\zbujze6hyu-{0}-rt5a291rko-rt5a291rko.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe#[.{fingerprint=rt5a291rko}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "83lvqkk2o6", "Integrity": "fJngxmFEJbrxzg0XB1CAG9KgieIYn1l1dZJkQQAyuNQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2103, "LastWriteTime": "2025-08-13T05:32:29.8831241+00:00"}, "qAVv0A/SPWXElvtfFbJvrpv19AgRHZKsmsvIJG/ak8Y=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\b8i6749yez-{0}-gigtt0ldg1-gigtt0ldg1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC#[.{fingerprint=gigtt0ldg1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8rbkdoj5f0", "Integrity": "hHUvv/yqwa5zSgVWC3cNgaie0IBPHVx2araCk90A8qE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3019, "LastWriteTime": "2025-08-13T05:32:29.8901298+00:00"}, "MK6LPRlhdlKRtMA8byK3zltna+nzDe62sLgtVdiuDOU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\jrpmfwb7su-{0}-kaw15hufc0-kaw15hufc0.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions#[.{fingerprint=kaw15hufc0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klk5o6a1ur", "Integrity": "eDg28Sc9ZdbZDpt3k13W5g6ETjTaQHGqgM6bHTwZ6Gg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2971, "LastWriteTime": "2025-08-13T05:32:29.8941253+00:00"}, "FR77C9euzs5iWXMPfgtx8QyvS2qPkbiuoct/vWXn13U=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\6pm647xnn2-{0}-7qypx0bvu1-7qypx0bvu1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles#[.{fingerprint=7qypx0bvu1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vu1p97a7v1", "Integrity": "p1oQhS813ZIWjDH8uFzEjV55o63p2wvw00GIMmKTxU4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2182, "LastWriteTime": "2025-08-13T05:32:29.8961256+00:00"}, "LP5BiuMFW840K/BTZ9nvp90T35peuhQZc7GduKzd9Js=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\631h038nxp-{0}-k67jm10rbw-k67jm10rbw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript#[.{fingerprint=k67jm10rbw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s4n923tqul", "Integrity": "pKSME6ZoRXjXZblt9MxuBLkUJOsDlq5idXAhI6Z0NGo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 30935, "LastWriteTime": "2025-08-13T05:32:29.8751246+00:00"}, "h1BcU933SkrCmcYHhqswkPSAyJn8KrOI0/32DOS719M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\b0jqemwfae-{0}-uanr5ywdiz-uanr5ywdiz.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation#[.{fingerprint=uanr5ywdiz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r4neqh2u3f", "Integrity": "ZbW9kNHYjqPkDJBZb929/PuDamKE+/zpbaQD+TjgK6Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2131, "LastWriteTime": "2025-08-13T05:32:29.8801247+00:00"}, "355I6l7H1bt+1z91sdz/h3AyceuObY45SAir0vzp088=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\iitqlnnjc8-{0}-fel5k50x7l-fel5k50x7l.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices#[.{fingerprint=fel5k50x7l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hlvjbk6khf", "Integrity": "tUGxnRqoGP9cIMCN+B7hKduRAJTUUociWZmUFWzGbto=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 23214, "LastWriteTime": "2025-08-13T05:32:29.8841238+00:00"}, "zkeQxMEijQThq05j7xfn7SaihWrH8Pv6cUw+/nz/DAo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\6n6ju6tqrx-{0}-eoagj84dsy-eoagj84dsy.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics#[.{fingerprint=eoagj84dsy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtwgb7d5hg", "Integrity": "2X4j4fLfnOXsqoISopLVaqIV8lf4oCc8PFeEF4BeMoo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2708, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "x9VlOGbALE2gizc9hBq+giaIBoh66pbHNZiK1/rTt6g=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\iy0tymru1v-{0}-7g62ykjls0-7g62ykjls0.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader#[.{fingerprint=7g62ykjls0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8a85g5gtko", "Integrity": "0MIqzbe+pffyM0T/lZsrZnFLNPwP6ncIchPngPmu5h0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2292, "LastWriteTime": "2025-08-13T05:32:29.8941253+00:00"}, "gVQsFGRDYCirczC1Dc7958wFMOTFAHwzetgfqFUfokM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\la0xlwigkz-{0}-tp0shtj6gv-tp0shtj6gv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics#[.{fingerprint=tp0shtj6gv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "86rfwy62na", "Integrity": "UP8OYsKxiTMEBjZxOl2KuoAJyXVs0lZyjkMf7piGVSM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 52000, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "5YsaoIbIaOXUCwcqjudwA5N9j+bYkuqbeNQkBcJQr+M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ki6vvhlx4j-{0}-nvsnsgm1il-nvsnsgm1il.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters#[.{fingerprint=nvsnsgm1il}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i26ba3epvz", "Integrity": "hrYFHqG6RM2GNpIQPTLhcpQ0hmtC7z8Z1iByziM2F6w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 24079, "LastWriteTime": "2025-08-13T05:32:29.8751246+00:00"}, "yINjDwORhU6xk2QoiQ2YxeFHwtZkNmYlAaClKH0njsg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\zx7pp6g7y1-{0}-4t62p34f9u-4t62p34f9u.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json#[.{fingerprint=4t62p34f9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i3v6wah9o5", "Integrity": "7ro/CctMgQB0qz3PdOdURIXFFcv3gIQYlvUaPoMSwys=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2231, "LastWriteTime": "2025-08-13T05:32:29.8801247+00:00"}, "JJ/f4WZc7/qC8UyaYsaWx1fYWirgCi4dIffIM/3/0Pw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\s67dldjufq-{0}-8mh3k1xubv-8mh3k1xubv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives#[.{fingerprint=8mh3k1xubv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3phssebu4p", "Integrity": "lwzwlXEfuqFYokvJkKfqc0oSaa4eT3CJOd8cVZTJXBE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5393, "LastWriteTime": "2025-08-13T05:32:29.8821247+00:00"}, "xXHipFeKbbQt/6RaAzxZSXdtGvLuCNCp3lcjW6+CJz8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\tdir3phpww-{0}-6heyz9oosd-6heyz9oosd.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml#[.{fingerprint=6heyz9oosd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ojxklpg90s", "Integrity": "LKB7cp1tD2LtqoKC3d7RPLWdg59qQkfm/+eoamoWvHE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2533, "LastWriteTime": "2025-08-13T05:32:29.8861239+00:00"}, "nKRLFMOrWVPaJhYupzmRnd7BPBMCSSkj+dW4AXspnag=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\z91tpfw4zd-{0}-1oa8jl3amd-1oa8jl3amd.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization#[.{fingerprint=1oa8jl3amd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q7q6cnhefb", "Integrity": "NqIv1N5ehS9mkQGrbjlOW1P1KPr6WiLhY6I+2z6RA/c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2479, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "x0kuOEeUYzPADwagMqEkxGiiFl2pJa/iZ/oAIU3GhCE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\cdu8x65bqo-{0}-xqvdvko8po-xqvdvko8po.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime#[.{fingerprint=xqvdvko8po}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "670w9y6opa", "Integrity": "/mV3vaq0SNGKFQl+G1sBgkmd1OdWYDvbWU/Z+UoFEg0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10642, "LastWriteTime": "2025-08-13T05:32:29.8961256+00:00"}, "0i3TDw3yMMWap/04WFhB8PIm9sEDA2oykiu9QoU7qwM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\lb1qq5ox57-{0}-jtnq7vre8d-jtnq7vre8d.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl#[.{fingerprint=jtnq7vre8d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nretya36yy", "Integrity": "A2rzxseDiIw0JQ2IYnmlTQ0VXfn1Q+aaWF9hSATBqp8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 16798, "LastWriteTime": "2025-08-13T05:32:29.8751246+00:00"}, "AKkgqnfdOPuWtD0u6gldLKz70C9J1wdeNIdmETkiGuw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vvxsqfzb9f-{0}-9fyr8onzdl-9fyr8onzdl.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims#[.{fingerprint=9fyr8onzdl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "28lhf1e0wt", "Integrity": "ToexBqV61zf/Bvghud+1p23vPF6oWCRmL8cYO5jLMq8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 16078, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "kL5hyxfNaaN54yepc89FsYeFKHzqJGgyB7bYcjvOHJA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\epvgw622kn-{0}-49z3p61zui-49z3p61zui.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms#[.{fingerprint=49z3p61zui}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nsly3ywhy3", "Integrity": "KXL+elsqJ/rMhBV07D/vkYs34LDvwBMKdwgwYrflmDo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2679, "LastWriteTime": "2025-08-13T05:32:29.8841238+00:00"}, "jbjdEZ19ax3Do5qFGQ1hHmUSs8zo0+A3+AYQZcBpJLM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\gnnffos883-{0}-01efu89mjc-01efu89mjc.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng#[.{fingerprint=01efu89mjc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "es3wc4kn39", "Integrity": "Lvcg6dyFdL8FkgAVLmjm5r0wHrK/y8JCgENNK12j/kg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2446, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "8+upP30QV2hpnpJQt3nlcJWamKs8QmFo0/CJhkyOZXc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\kcsr0jlkzs-{0}-m6kt5rkphi-m6kt5rkphi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp#[.{fingerprint=m6kt5rkphi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iryetbad6q", "Integrity": "ftDk4F0mlVkVCqbVVWHLF0rn3aq3ZST1Vq/9fFwPRfY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2311, "LastWriteTime": "2025-08-13T05:32:29.8941253+00:00"}, "MgVQpK6UPnd/1oSuNvqP73KwHum1X2shJvclvjZpJf8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ykbxr1r7y8-{0}-4j2304etti-4j2304etti.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding#[.{fingerprint=4j2304etti}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xaplqpxjn9", "Integrity": "p17RN1tUF2uAAKF99fklrvIHVFcHkYN/DdVRaXY2ges=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2254, "LastWriteTime": "2025-08-13T05:32:29.8961256+00:00"}, "mKr7Q/wI1G66KSILIdh5mhbUUctmkAkebX9UqrCTxyY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\t169uoi1u2-{0}-rgf4gnhaju-rgf4gnhaju.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl#[.{fingerprint=rgf4gnhaju}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "orhizatwft", "Integrity": "jJqnGUhBv0KWzW2hVUCb/MHliXR/2tLmi9i28xOK4ww=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2184, "LastWriteTime": "2025-08-13T05:32:29.8751246+00:00"}, "lcKBDAoXjMv0wAg6rCX39jZxwE9jeoVQAYVkbNbIIRc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\8yojc0cb84-{0}-wqi94vu5m0-wqi94vu5m0.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives#[.{fingerprint=wqi94vu5m0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c1hfgw6cu2", "Integrity": "APQkRu5wL3dD+uSkEjQgytiFJM5RsFaVyYJ8j4rYeHg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2309, "LastWriteTime": "2025-08-13T05:32:29.8801247+00:00"}, "P6NGYiwgpfUlKKabPQqiPgPnWP6jBtnqwBcF8r8mOow=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\7xj4vtlbe8-{0}-e4s9csihna-e4s9csihna.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates#[.{fingerprint=e4s9csihna}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z323cdc7wr", "Integrity": "b40EE+RBEVB47m6I6ylNQkmVsTRRWbXE9vRXqc4M7dw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2645, "LastWriteTime": "2025-08-13T05:32:29.8821247+00:00"}, "0lU5FN6w69FohSVp0ZbyBfiYC9L3lc+i+ukWmKjRLWY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\9knoi3h2b5-{0}-z9o6jihhaw-z9o6jihhaw.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography#[.{fingerprint=z9o6jihhaw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h7m0rhrjps", "Integrity": "bb9nKlZtFF1BWonFQCUHfrx8njTrkquPMbL2r3r5cEk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 190176, "LastWriteTime": "2025-08-13T05:32:29.9081247+00:00"}, "WrZWGLw1T8rcXJZWhHsoE7TL958g20gfz/NzXanabV0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\3qubpab9b4-{0}-d93pggsupp-d93pggsupp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows#[.{fingerprint=d93pggsupp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zcteqrh34n", "Integrity": "IwJ/rRn6GhYp8cazSuPJhJQfC+CA92Ay3l5qV+eyzDY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 11068, "LastWriteTime": "2025-08-13T05:32:29.9091244+00:00"}, "COCf+xrMVkCTcDANNbZ5JhOUYVnZAOYGRgCzqMOSFvY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\bv78doa1nt-{0}-sa193kq3m2-sa193kq3m2.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal#[.{fingerprint=sa193kq3m2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "etxu6a8wiu", "Integrity": "srTk/Q4MDmQcMh6I7jQxA7rBiiSj+z/mToT4O/mmy3Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2137, "LastWriteTime": "2025-08-13T05:32:29.910124+00:00"}, "4phogNfhS0Zd8LpXFvRISxyXChISVHuhNeh8M0OIrU8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\rxhnrlxubj-{0}-z4ma9duddm-z4ma9duddm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString#[.{fingerprint=z4ma9duddm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qwz0oxftca", "Integrity": "Nt5VraBobnvAewaGwAXfphktxnQGbSX61kYtd+gRMpQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2173, "LastWriteTime": "2025-08-13T05:32:29.8751246+00:00"}, "Q3uRUTD3WlSdwQc1ojcD+7xOnRFgdQ+EdzRmWn0EZqk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\y1ooyp9uc5-{0}-rztf0whns2-rztf0whns2.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security#[.{fingerprint=rztf0whns2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ma8x7rzffi", "Integrity": "wrSJEchRkmmwsxW89uMD/ePtANgxSFbnZuPYfcy2UTM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2945, "LastWriteTime": "2025-08-13T05:32:29.8791247+00:00"}, "gQ+QYcsqO8ruDXbg/6jFGmrzoDH0T32J11Dsk3xxBXw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\gdk781dj5t-{0}-bnlcmxi1w6-bnlcmxi1w6.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web#[.{fingerprint=bnlcmxi1w6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wbc09gg9r6", "Integrity": "bGfFZWSQ351UFOD8B8Qph1RG4uVhX9sy5zyIS8Qstgo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2519, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "zFE2PDGJBFiCHZC+Qh9ynmzGajXkMkTFwAmp7TB5j5I=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\0ae51qwp0n-{0}-drbdk7bquo-drbdk7bquo.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess#[.{fingerprint=drbdk7bquo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h2vyoxnv05", "Integrity": "yT1Xhwlsf0oKRFTBfT8gm50+UA4LmE2mxq7AsjD1Gwc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2283, "LastWriteTime": "2025-08-13T05:32:29.8851242+00:00"}, "Mg8S0Q26GMoYwH/I5pg3ziw91h+8M/c99h7HNshYJU0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vw35pjcn0b-{0}-7iff0d2lb6-7iff0d2lb6.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages#[.{fingerprint=7iff0d2lb6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7s<PERSON>uw<PERSON><PERSON>", "Integrity": "meWuhZKGhfH6y4UV1Z6f3IRXfOHFODg28ZnKjn9ouTw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 514863, "LastWriteTime": "2025-08-13T05:32:29.9131238+00:00"}, "THZejE8EZ5P1ON88N9CyCCh1vPPPDxwxo5mKRWqkJrw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\x9outkujcw-{0}-dc711vstge-dc711vstge.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions#[.{fingerprint=dc711vstge}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9k9vmojgla", "Integrity": "Kah4fRKsKAd0wXhjBVdT4jmRAn0XpBk2nctc4sDI16I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2231, "LastWriteTime": "2025-08-13T05:32:29.9131238+00:00"}, "N4+VJaLc6ORsEi/t82HCb3TVNn+DJxs2yZKI7SLwN5g=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\j52rvyj0yw-{0}-3696nx7xrc-3696nx7xrc.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding#[.{fingerprint=3696nx7xrc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xdp6p2epv1", "Integrity": "Bec0wDFpEZVn09cIQ3AuMN11ktIfSjfv93kglrCx4gA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2217, "LastWriteTime": "2025-08-13T05:32:29.8751246+00:00"}, "n4gmCHnPvxDLeJCKAwYHbcimz9iY5AxtFUml+J+0SlA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\3v2zcik8bs-{0}-ksemyzm5ld-ksemyzm5ld.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web#[.{fingerprint=ksemyzm5ld}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pikfidvm24", "Integrity": "oSthMNIhA5EaiZ8+8Qt+OOMRx8TR89oskgWyDut724U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23403, "LastWriteTime": "2025-08-13T05:32:29.8821247+00:00"}, "VtuRBpqlR4UQZ9fl8w/J5Qx0/M0QsXr16rrA2/PIhm0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\e4k2mjuyl2-{0}-x92ye0v3y1-x92ye0v3y1.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json#[.{fingerprint=x92ye0v3y1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i0vtayb979", "Integrity": "vrvfxyAfAX3kRXZsNkhaNRVleyuooDPY47GH2USFUR4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 216853, "LastWriteTime": "2025-08-13T05:32:29.9131238+00:00"}, "iuW4HlWFk8QdN6bgQaEu1nuRJxN4RzOhwUAfv5VKrl8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vaf1psfujv-{0}-dnj9z23s0g-dnj9z23s0g.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions#[.{fingerprint=dnj9z23s0g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rkp1fd9cpw", "Integrity": "RXWosrLgF1na33iZUQZyGegZeRZZUKCHIgInWNZErQc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 153662, "LastWriteTime": "2025-08-13T05:32:29.9131238+00:00"}, "fFRk5iqchaOV2k7tim6fjxgwZkEt2MmCHm6NK5ECWC8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\mvd69cc4m6-{0}-tomvzoqfcf-tomvzoqfcf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels#[.{fingerprint=tomvzoqfcf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jzp23cb6oy", "Integrity": "D7NZRTviiZxz4BYPW3d05UykXtr6IS4eg/hYYydqDs8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 20565, "LastWriteTime": "2025-08-13T05:32:29.9151238+00:00"}, "2wY1BZ3FGpyw6TBgWragqY4s/sx0UeyKFcJAodvfboc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\naswe3dl75-{0}-0g3k20op8c-0g3k20op8c.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped#[.{fingerprint=0g3k20op8c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xw4wsum4as", "Integrity": "H+tNtMOjpdCXKy6k1MsZuT5kX6kIved5yv6KJcQgptw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2275, "LastWriteTime": "2025-08-13T05:32:29.9161239+00:00"}, "/9ho7gJ9O9PWweMILViGwec4DJof8NyPNUy/pk1anQI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\wn3z8nzxqv-{0}-55tewhp7kf-55tewhp7kf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow#[.{fingerprint=55tewhp7kf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "txf8gv8e56", "Integrity": "TxHJkkFeTafy8vSrzj0N5iyWfdVDfPc3q0x7bND/XvU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 73169, "LastWriteTime": "2025-08-13T05:32:29.8841238+00:00"}, "wDnRDfPrZKuiVX8khLk+nCPAmSGpstvBIz42p/+UrpI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\5ep0p4n4f2-{0}-sce61xpslf-sce61xpslf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions#[.{fingerprint=sce61xpslf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4uu3byrvxy", "Integrity": "3OHfdFy1Jx8F3e/u5Q2euVV8IqTSrIYGoSSkaGxq1Tw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2276, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "fHN2amvazD9319nvEA59O8jEDSlwlzXiWPyCq7u6iXg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\mq3le2ogsr-{0}-548crbk151-548crbk151.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel#[.{fingerprint=548crbk151}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3yq7kjux9s", "Integrity": "1ZscPBr7BfbBVF6vdaGTGwc2Qyp4e1gmq38qSYqUbVk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21027, "LastWriteTime": "2025-08-13T05:32:29.8951254+00:00"}, "JKWJ32elzs6vaEmLPIZPJsbgfidb/hN7J79Jr4K5Y68=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\xlbzli35oc-{0}-x86n4j91or-x86n4j91or.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks#[.{fingerprint=x86n4j91or}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cgw3jmu428", "Integrity": "9E19Texq+VKY0AneWPPKfpQ4ivIt6jF40rgqxZw0Nkk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2544, "LastWriteTime": "2025-08-13T05:32:29.8981261+00:00"}, "Heh4Jj0IHOuajRgveUM9iXKRIRZhTjmDBQ1DacV3lv8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\5pyj82ayu9-{0}-07bttawl88-07bttawl88.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread#[.{fingerprint=07bttawl88}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "toxfy3um1p", "Integrity": "s/lKEXuBT/L1b4dNP5U1xTlb+2/3cIK/g70Qo8V1uls=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2310, "LastWriteTime": "2025-08-13T05:32:29.9001246+00:00"}, "miqkKdtipdqFCh2FaBSaKYNeTS+8V5qiEMuwaf6V3pY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\p2ujx4rie4-{0}-zt447d1n6v-zt447d1n6v.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool#[.{fingerprint=zt447d1n6v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bwuihh3yjg", "Integrity": "FU5FB6WMQdfx5vUH+IYKAlrMPNh6UEC/nlvHjyoswZE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2225, "LastWriteTime": "2025-08-13T05:32:29.9011249+00:00"}, "tgRjwMd9N32C4gLX7n+CTbznWYBwoBmZblpzGF0VtfA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\rqp8olh4yh-{0}-r3c1h58f9w-r3c1h58f9w.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer#[.{fingerprint=r3c1h58f9w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qkrg4ykxwk", "Integrity": "ZkTgB/SsBNHcnjMj37TOuqw6gUkKhOSpcvImLgBX53k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2107, "LastWriteTime": "2025-08-13T05:32:29.8761246+00:00"}, "CJ01+B/ZnXYOXalAqwETlHQVWxfvZgqq9ulCtPrsKEQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\3ozuy9kc4p-{0}-4z6mzh73ny-4z6mzh73ny.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading#[.{fingerprint=4z6mzh73ny}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0mx0dzvzkl", "Integrity": "2Aww/uGQzCjiT5/c3oSBO/6JdcHKsmnNT3LD3ts+Xzw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14647, "LastWriteTime": "2025-08-13T05:32:29.8811252+00:00"}, "P9KJJHEfedqOgrOWDA1ICoxVVByLnbwltx73F8dPmY0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\d0l56xbf1y-{0}-pil3cjgvw5-pil3cjgvw5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local#[.{fingerprint=pil3cjgvw5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tgyb8rhhdv", "Integrity": "w9hfpmhSy+CjV8OQLZg/GLKwP4HAES5zi5k0OGRk3XA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 51417, "LastWriteTime": "2025-08-13T05:32:29.8931244+00:00"}, "+0Uds+4kzQQ8Nj/8MzIG+EfmkxgDwe1v/DcmoEXFDuU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\bj1ouvglsc-{0}-g6ni30uafv-g6ni30uafv.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions#[.{fingerprint=g6ni30uafv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qx6q07q6ty", "Integrity": "nkF/Bfe7Uhcdxv7fbkb/COJ1vn+OYG2qaOUw5m8stWc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2355, "LastWriteTime": "2025-08-13T05:32:29.8961256+00:00"}, "j3Hltk8AW97DHcfVQlYzJGXv5fR728nKXbRg4rjtbHA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ttrczax0uh-{0}-adv6hyw1vi-adv6hyw1vi.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple#[.{fingerprint=adv6hyw1vi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cppza7n2y6", "Integrity": "IN2amQpSBFalP3cF5TcpkOD+wJXh4AMe+MyVcEJ7DpY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2150, "LastWriteTime": "2025-08-13T05:32:29.899125+00:00"}, "G/DN/XSnlvQrU7WNEmN9CDTiTTQpTSe51WJLkXuNI5I=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\mgktwlr6fe-{0}-4yi0atwy17-4yi0atwy17.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility#[.{fingerprint=4yi0atwy17}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jhpxtwjltp", "Integrity": "udOxbi2cMNj3fkVWnbMyDZ3AB4RoMXWEBc2xr54GKTM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 9831, "LastWriteTime": "2025-08-13T05:32:29.9011249+00:00"}, "RQVsRsP+MNPqSGS3dKOnsVf7FtnBvAkZXYWdvj0iYoM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\7s7ycd46az-{0}-8uickrr2w7-8uickrr2w7.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web#[.{fingerprint=8uickrr2w7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h2o3mijqak", "Integrity": "mapgm8dKEFFphk24qhSoI7CzYDGzFTTuNqEy2IKo45E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2098, "LastWriteTime": "2025-08-13T05:32:29.8761246+00:00"}, "WH9AvpRkA6ZBBK20X5FRywNcx9LxwmimYNWYmKGwbWE=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\lb5fihpb8l-{0}-idlgil0u1u-idlgil0u1u.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows#[.{fingerprint=idlgil0u1u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "91ccacz8t3", "Integrity": "7fFS1d6gaaEKkuiGXD5NCN2mux3YZWrd7Q6L/e0Wxlg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2260, "LastWriteTime": "2025-08-13T05:32:29.8801247+00:00"}, "kFV1c6CViehU5jauPoXsDzFSdeA4WPeXuMxQ9Se0GCI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\roedkyhu2w-{0}-jhjtvo31q0-jhjtvo31q0.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq#[.{fingerprint=jhjtvo31q0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ivhc7pi274", "Integrity": "gg8npzWUagbqIvMul7uv3zis10lc0ThXft4PRQs1jSg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2189, "LastWriteTime": "2025-08-13T05:32:29.8831241+00:00"}, "pszna5iXTryCet5CP8URMABwRLgi/PEEs48lvneVgus=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\tofgqw5pjx-{0}-pcqwh7wu97-pcqwh7wu97.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter#[.{fingerprint=pcqwh7wu97}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "naggvnx24o", "Integrity": "gX6T2rd4NFnDWNqXwG+Jx8PW7mAbVEM7OI6P9i4SYrc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 3982, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "g4m4AV4nSzJT8fn4lrCVTGVoZWLoVwxO/PVqMMb+GY4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\o8sl438lvo-{0}-0x6beqi7zp-0x6beqi7zp.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization#[.{fingerprint=0x6beqi7zp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lfd43nw62j", "Integrity": "/8NVRec/qhGnIxiW96iB1+zQ2KlPlJBwiDybckJ3AqI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2214, "LastWriteTime": "2025-08-13T05:32:29.8941253+00:00"}, "HpjElOvDWnID5zM2pjpGjgqaJgGve1AF9nqzSTeyk24=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vwr477b0go-{0}-8luigpl137-8luigpl137.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument#[.{fingerprint=8luigpl137}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oy0lmn48yx", "Integrity": "IMprs0y7UdMstD+EhkTWCRpYuix5dYxaEj/hX9+YfUE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2370, "LastWriteTime": "2025-08-13T05:32:29.8971253+00:00"}, "TfFadMtnwg8parqm4/c6+Ka8r+nYQPdk3zZgxvO0hlA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\c0ybvnd0yx-{0}-rps120mzwr-rps120mzwr.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument#[.{fingerprint=rps120mzwr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ttlupxhww", "Integrity": "K7zNZv+NDRhulrGmQl5MNiotH0KumjSmah9MI2Fso1g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2458, "LastWriteTime": "2025-08-13T05:32:29.8761246+00:00"}, "FaCvrFUCHMHDoYvNU5LV+1Nl5+IivBhFwpx/c0zEp+c=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\8m7dfebq8z-{0}-nj6o6nhskf-nj6o6nhskf.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath#[.{fingerprint=nj6o6nhskf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5zddknzc6k", "Integrity": "//egFUKbX7X9AjOEL6G6FxxhFq39+qATP9NWaZr3aWc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2292, "LastWriteTime": "2025-08-13T05:32:29.8801247+00:00"}, "oHSpQrtc2cTXclP/JyG3MjmZ2i+h9O3pWkpE9C+It8U=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ffbv6j6gp7-{0}-t7u25q5to4-t7u25q5to4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument#[.{fingerprint=t7u25q5to4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v8bdmckfxd", "Integrity": "0f9KXCThCFnmDON+jBqfx7qPYCh6698wR2snCrlBNhM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2331, "LastWriteTime": "2025-08-13T05:32:29.8831241+00:00"}, "vZRGMkF4bzy3BJ24cy0N5cTMnK0uiQF2pVy1QM/Eq9k=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\t2gwaq6x9s-{0}-ig2qir1wep-ig2qir1wep.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer#[.{fingerprint=ig2qir1wep}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lskn5gtgx5", "Integrity": "+wEXQ4BiPdOyNZxoBhw+wOChQvsFTysH9kmol19348U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2825, "LastWriteTime": "2025-08-13T05:32:29.8911255+00:00"}, "1MlI3ON0H8xLmHoQSUdnW1QydT+BejEUrtcg/q6HdHA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vnx203s4w5-{0}-lnwczuoimm-lnwczuoimm.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml#[.{fingerprint=lnwczuoimm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wlxcj8wjji", "Integrity": "JAjB3wgIGO98Cn//2MnZxyyVQTSSHdD6oM/FvrdbC08=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4200, "LastWriteTime": "2025-08-13T05:32:29.8941253+00:00"}, "PtEiOINaLTYcuM4nRyUmlAS947Z1WwfUcAxE/12T2Fg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\vbwjwnvk9f-{0}-00ls1afmp9-00ls1afmp9.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System#[.{fingerprint=00ls1afmp9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7sejobmv2f", "Integrity": "aeoSbukv6t/yOSgWEHA1igIaVkr0OHS+8UV7iqHy6Lw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11756, "LastWriteTime": "2025-08-13T05:32:29.8971253+00:00"}, "c6ZqZKeal/VGj8EkmGwN18ZT0lgDk1Lb6nhdeLz/b6I=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ywopjzzmnx-{0}-u25hol0de4-u25hol0de4.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase#[.{fingerprint=u25hol0de4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iiv78q38j2", "Integrity": "SnsxrEkol2MLkwcoja+4dLRzJs8CoDaExyNVW3d1jSw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2503, "LastWriteTime": "2025-08-13T05:32:29.899125+00:00"}, "uuDrwmb7eLsAJzZkVERHrDpt9TqkY1xd+d0L+i5IHB0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ehrglyz6r7-{0}-brg9pkj3je-brg9pkj3je.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib#[.{fingerprint=brg9pkj3je}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pwp0f37f43", "Integrity": "1/EPTD6B5ASqxP1jEDD6n/Is3uhJjMz5xc+SRZDRAUo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14805, "LastWriteTime": "2025-08-13T05:32:29.9021251+00:00"}, "QCtbAz7U+Y6j8TDnXDvS3aCC/zik8f/4TJkQOV+oyrA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\b1a45mzccv-{0}-wuzd3f1y6v-wuzd3f1y6v.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard#[.{fingerprint=wuzd3f1y6v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2e01rr9wd8", "Integrity": "xnCvHe5T6PfmwOed9Q4j/PovOzQ3HruHJ1stXUUrnM4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 26065, "LastWriteTime": "2025-08-13T05:32:29.9051243+00:00"}, "eLk1oGciDumTkIMLD0HB/nOKYh0nJl8TFQ4fe/TkR34=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\92nwqxj4ji-{0}-05ksnw82w3-05ksnw82w3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib#[.{fingerprint=05ksnw82w3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vb2xp0sgje", "Integrity": "CwNtbQxPvM01XG/8EO2U4VO3mHIJ1m+OcGOx2CvDdY8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1496481, "LastWriteTime": "2025-08-13T05:32:30.0301238+00:00"}, "22m9Nmw+wiAPL305ksKzWZMKnJWfVAqdBPglwUAwSRk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\atfbiuprug-{0}-vr46os3pyt-vr46os3pyt.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet#[.{fingerprint=vr46os3pyt}]?.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7ff616hass", "Integrity": "bWMnF/Yt/Qzsdc2QEqO4wwUvxXMZILPR3YoKTBQUZSc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 12661, "LastWriteTime": "2025-08-13T05:32:30.0311243+00:00"}, "iXTiUOZrsVX2uoTFml+YD28x86bu69FTaFtU2eU1QQg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\kqzxlj4q1l-{0}-es3ekshrlb-es3ekshrlb.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js#[.{fingerprint=es3ekshrlb}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9l6pu8fhxf", "Integrity": "Y+Q2tpO1byf0qWtoU3Lz8ONySK2aGlH+1Fj5ioT/avY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 21067, "LastWriteTime": "2025-08-13T05:32:30.0341238+00:00"}, "c3KqTBQv5X/SOw7NhxTqVW7Ju/0Dvb8jMiuhVSoerVk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\mx60r437t4-{0}-rtblh4npr3-rtblh4npr3.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=rtblh4npr3}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ygpzgcwfny", "Integrity": "suuJwSBHkvFASeaEZwAJnUWiH9GvpsougJlVezmdaMo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 34748, "LastWriteTime": "2025-08-13T05:32:29.9041247+00:00"}, "jc9PHXx9XyZvAPKLgZ/Z1mFMeiK95olwGw6xiqtGeug=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\t451ifx54a-{0}-aqhezbunpl-aqhezbunpl.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=aqhezbunpl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v0k6y61mro", "Integrity": "IYvAT4mFHVPDqOh7OoBpNKyj2ml+C4Fzzn1TmEd8ce0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1179217, "LastWriteTime": "2025-08-13T05:32:30.0361238+00:00"}, "1ZzNt1aea2c1l88gW0ff4agCU4e6iIIe6aWUc+MjA6E=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ganheevyck-{0}-d1pzlaz2ez-d1pzlaz2ez.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime#[.{fingerprint=d1pzlaz2ez}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a9volc0z9v", "Integrity": "wqbMmW2BbDbOUBaVhW5c0+N9a24THChw4MuymiWOJ3A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 55607, "LastWriteTime": "2025-08-13T05:32:30.0411239+00:00"}, "FsIXAUGtjadI2hDJTCUnI9u8+OJ12+UvUuEjetSXhLk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ehd7i69oga-{0}-ctf2q9h8m2-ctf2q9h8m2.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js#[.{fingerprint=ctf2q9h8m2}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1samrp6awr", "Integrity": "70CNmEpfHJuxx1KcZjmtlM6EQDQFQ0UUsqhVZ1FiynM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 87809, "LastWriteTime": "2025-08-13T05:32:30.0521239+00:00"}, "JAB09DK8uIumhg+I8f/DBHKOnvRj1i9MoOgYottqW1Q=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\ajonspo973-{0}-tjcz0u77k5-tjcz0u77k5.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK#[.{fingerprint=tjcz0u77k5}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "su9h2nea1m", "Integrity": "JKp+T1EHUj4qBIqOq6CqjdfXcSHC5rZmYtsjCDiZV4g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 333110, "LastWriteTime": "2025-08-13T05:32:30.0791237+00:00"}, "wkZqSXmU25AN6CaFCv/5uCgct8XjhkV7QWHv9Ksz8aw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\yg5ej5faok-{0}-tptq2av103-tptq2av103.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS#[.{fingerprint=tptq2av103}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnxfkgr4e8", "Integrity": "G9yz26qggmFJkfJ5kv16IEEiVrEH3fuBNu6MzZ+3hRE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 196037, "LastWriteTime": "2025-08-13T05:32:30.0931237+00:00"}, "5elvW4VSlZFAC4TGadF1CUUvydbYrfc/Iu83ekWeuwI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\z0rgfvfjxg-{0}-lfu7j35m59-lfu7j35m59.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK#[.{fingerprint=lfu7j35m59}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v385ycndre", "Integrity": "S3rRs+MOdWkA48i3UrKbP0iD+IShrxe0Z0ZuQ7Mp9qk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 317618, "LastWriteTime": "2025-08-13T05:32:29.934125+00:00"}, "mA1+ARRoQxUFuWlijJBWyYXkla/nNmRUA7apHmS5SJU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\nepntx6xic-{0}-zg0i1aycqh-zg0i1aycqh.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ZeroKnow.EBS.WebApp.Client#[.{fingerprint=zg0i1aycqh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\ZeroKnow.EBS.WebApp.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "extet9v4m1", "Integrity": "AoAH1W0L/XMfmru8XH7F1FjmDReXSaA10d8V9/PwDSA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\ZeroKnow.EBS.WebApp.Client.wasm", "FileLength": 38098, "LastWriteTime": "2025-08-16T04:06:38.2428814+00:00"}, "u3J8Uv5GXnuRtBkln2lyem0GGwGtZIDtZOcQoNYq3OA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\utjp9azvus-{0}-ioy9coeg2d-ioy9coeg2d.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0n2wihq9jq", "Integrity": "pBQEqSpYhkqqTBdP+MDyqwivJkAFgvmHDiSFVnSBMUo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 13281, "LastWriteTime": "2025-08-16T04:08:08.733318+00:00"}, "J+RLQO7Wvc72LVHVUn2HSGL6fU+sWu2BS/O7kIN3uAY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\l8lh1f8xrh-{0}-s8ryayf1u7-s8ryayf1u7.gz", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/ZeroKnow.EBS.WebApp.Client#[.{fingerprint=s8ryayf1u7}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\ZeroKnow.EBS.WebApp.Client.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fgyrt1u431", "Integrity": "L6iZsANES+UpWcrL6MlbnAqdaAjy679hbdLiHGHua6A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\ZeroKnow.EBS.WebApp.Client.pdb", "FileLength": 56060, "LastWriteTime": "2025-08-16T04:08:08.7323148+00:00"}}, "CachedCopyCandidates": {}}