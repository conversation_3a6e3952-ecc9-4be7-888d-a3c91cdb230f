{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["E9MwrmvRf2TIccX0uHiBS0pHO0Doe/NtbQFyDuQrdQ8=", "t+Shtc5VRvciKzNaD/zcktuLHR/UvRsZPoH3fyzMZfo=", "x34CKnN3q8LKzNmMl9NpRV/oMqd3UMt3yHpXykei6i8=", "zNeHwVd8Lb15nYt16Bda+YQ3vWpss1Tz5GE32HcTG00=", "L+OnpBFNijSNzw7lpoykbWpO1M8wSsodQr1zvr3pYq4=", "2ZtDkjUx4HYT0/IpOkYf2XGa1tDaqgF7LVq7gklJNqw=", "qnWyjlzyceL9T6YwcvOTHLv02cA4pcyoNWSnbnbhogk=", "Q63zZW4yQt18k4yE46HeVVPaJfs/VQ2b6yAWshiqXg4=", "i9qYXfeo1YUoFwSbJxRfcXg0ymBpHyn+wqqY/+UdHi4=", "f5FErblLBi3dNbAh2Il4evkr/Gd8RY26RB/3upLab7s=", "62zVLab7Jx2z5PpQpz2FpILPcHS6hN909KoyDLn3XF4=", "1Fp3mLMd5iFbzvLjnyEUkVN6Ghy2S3vuGxJ9R/sbUF8=", "nEbJiOxPenuYSMa/FAyWnnfkei017NZBeS62A10tiZI=", "pub5oVmN4mIo8voRFjxzHxK7SxXl7VVJbxNDYgSO3PI=", "7is9I0T8MfWZbcSIHCi1GZC20Fy3uqNG6swJHgg9yj4=", "1u6QgGlIxU8bkbZ3MdWpz6EnPICPpra6yT1sLBaJ/eI=", "msJMIXugfRbEtIfP3XJuzEXQMGm0LLvyZwhSUk2babs=", "kkXLYrWtJwXk4VuJ/u3zHo5P/ttTgHmkSPvBsPWAPXA=", "g2uFpjkberc9XtfxrezR+BILxnwuAUpfLcaN15E7rDg=", "9fVI014pkOZ8XGPGEufzBbiDUGx+KCvoIoibjyAjPHc=", "Yh4wN4UiawAgNtfDWF7aNr73oft7A9q2ihJ6yzKpgO8=", "S1cQfU4/aAsKeR8Td1SHUioSQZH6+TsFSfPfE4jfZ4k=", "tRXcSyYryoReqruKFh0NpmOnJ1xY+Uoak5k6GIn/0vY=", "X5miHwvHqtaBSwzIC/MXPPO6RG1Y8C54ViBHf1VmU5M=", "jxFF4fG2i1Ugn9xcS6B66Lh0vyp38fD8q6f1XOqf7s0=", "KqNEy6++364wkXaJjFDm3TL7mzUDBjr7U57918uDMr4=", "/wjMXMg73cCmsy00en6U7WRpdwSow/9c7U+SKb+7lxU=", "nPoTGjUP0XpNIcySwc2wgVWx89lQMAHIuKIMbObRWrk=", "IyM8qOYUS2G8xbU9sh7xe7nLR5GIjesCRcbs1+d3pIw=", "ROVQJbvkVdQahvt5veWXRbmzF+e3IttJI4oVvohrsJI=", "76pvba9LkWS3zOhArhFgN+EeMLJHnax5btbIOlUxaQg=", "6rkdcEDiwzteQ9CMf0+M8MN/hwnbvC6d+yYf8PbJl/8=", "ENWiWsInMyoDqbpHkG2AP6Q/aoxmfHyjCPDNTNjmB/k=", "VmQwnHQJvAsPsfV5YU83Z7A/tliMTelugYpZ3JGaAu4=", "ROeG25rxrsLu8cM+/C7C8WwkqfYSSmQlKRsp7NY8XLI=", "evRU0EfI8sCTH+fewwPK98dOC496AlKLIcS3Zo+iORQ=", "EjSq/EQc9Kn/DyX6HIz0v2dfWyMyNhKLi0jQLVXw9D8=", "EqM95Pl3Sq19Htp9FMxKPqxMXDs1P1crKTJgTizt+Wo=", "j03SYL87FXtCmgayxS4JP0eZfi9QH4rS98gG+0HTR2U=", "rt/9bX0si4G70YVBAzGCiufEoNXkKxeP2fs9wWImJG0=", "WoivKkik0SiCAt8mmLx3/idbXEVcI6Db0abqh0PomeM=", "BGXxebn/caVTZMvU2NkUwbB3VhOLwtIWINnKpNMcvm8=", "9b/bfTbV/zMjJ0DOBcR9i87cZek2Tcy2en2eQU1AhOQ="], "CachedAssets": {"E9MwrmvRf2TIccX0uHiBS0pHO0Doe/NtbQFyDuQrdQ8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-{0}-dpoev2zj9b-dpoev2zj9b.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fo9kun7a2u", "Integrity": "//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 267, "LastWriteTime": "2025-08-13T05:32:32.771974+00:00"}, "t+Shtc5VRvciKzNaD/zcktuLHR/UvRsZPoH3fyzMZfo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-{0}-819w3ybe2d-819w3ybe2d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "182vbtilql", "Integrity": "Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 1002, "LastWriteTime": "2025-08-13T05:32:32.7729746+00:00"}, "x34CKnN3q8LKzNmMl9NpRV/oMqd3UMt3yHpXykei6i8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-{0}-p6kf5zqzit-p6kf5zqzit.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cxryly5d9i", "Integrity": "ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 311, "LastWriteTime": "2025-08-13T05:32:32.7729746+00:00"}, "zNeHwVd8Lb15nYt16Bda+YQ3vWpss1Tz5GE32HcTG00=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-{0}-zjzit57lox-zjzit57lox.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0aw64dy5f3", "Integrity": "GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 217, "LastWriteTime": "2025-08-13T05:32:32.773975+00:00"}, "L+OnpBFNijSNzw7lpoykbWpO1M8wSsodQr1zvr3pYq4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-{0}-0s0aj5pwq9-0s0aj5pwq9.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8lg2m7f07u", "Integrity": "fAKRdfniIP02J3SgS321X95edwrgJkkhMjrDT6c8eGM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 3853, "LastWriteTime": "2025-08-13T05:32:32.773975+00:00"}, "2ZtDkjUx4HYT0/IpOkYf2XGa1tDaqgF7LVq7gklJNqw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\qtsc4kwma6-{0}-h5k564t0jv-h5k564t0jv.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DateTime/FluentTimePicker.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\DateTime\\FluentTimePicker.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ppjcy3oc1v", "Integrity": "zbzRK8rwmIQECl+yYiuHnVNefQF9Om9SjSLTBkuftJ8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\DateTime\\FluentTimePicker.razor.js", "FileLength": 181, "LastWriteTime": "2025-08-13T05:32:32.7749751+00:00"}, "qnWyjlzyceL9T6YwcvOTHLv02cA4pcyoNWSnbnbhogk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-{0}-vyjqmndgy2-vyjqmndgy2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bo7nvuwvlw", "Integrity": "m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 754, "LastWriteTime": "2025-08-13T05:32:32.773975+00:00"}, "Q63zZW4yQt18k4yE46HeVVPaJfs/VQ2b6yAWshiqXg4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-{0}-iy34mpf72d-iy34mpf72d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glesyzohpx", "Integrity": "5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 231, "LastWriteTime": "2025-08-13T05:32:32.773975+00:00"}, "i9qYXfeo1YUoFwSbJxRfcXg0ymBpHyn+wqqY/+UdHi4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-{0}-hi1gwvth64-hi1gwvth64.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9zocec3c57", "Integrity": "i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 759, "LastWriteTime": "2025-08-13T05:32:32.773975+00:00"}, "f5FErblLBi3dNbAh2Il4evkr/Gd8RY26RB/3upLab7s=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-{0}-5pcucyxosc-5pcucyxosc.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o73xmx6c30", "Integrity": "hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 138, "LastWriteTime": "2025-08-13T05:32:32.773975+00:00"}, "62zVLab7Jx2z5PpQpz2FpILPcHS6hN909KoyDLn3XF4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-{0}-vjluklws0l-vjluklws0l.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1sdzkmuo8x", "Integrity": "gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 834, "LastWriteTime": "2025-08-13T05:32:32.773975+00:00"}, "1Fp3mLMd5iFbzvLjnyEUkVN6Ghy2S3vuGxJ9R/sbUF8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-{0}-pu9hn1jugj-pu9hn1jugj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0dloqnx2v", "Integrity": "4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 891, "LastWriteTime": "2025-08-13T05:32:32.773975+00:00"}, "nEbJiOxPenuYSMa/FAyWnnfkei017NZBeS62A10tiZI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-{0}-xp2f0e0rh3-xp2f0e0rh3.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nmysnspdoa", "Integrity": "T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 251, "LastWriteTime": "2025-08-13T05:32:32.7749751+00:00"}, "pub5oVmN4mIo8voRFjxzHxK7SxXl7VVJbxNDYgSO3PI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-{0}-psptt994gq-psptt994gq.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b9j8p69emz", "Integrity": "gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 570, "LastWriteTime": "2025-08-13T05:32:32.7749751+00:00"}, "7is9I0T8MfWZbcSIHCi1GZC20Fy3uqNG6swJHgg9yj4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-{0}-afevzs963z-afevzs963z.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "inru90os05", "Integrity": "fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 544, "LastWriteTime": "2025-08-13T05:32:32.773975+00:00"}, "1u6QgGlIxU8bkbZ3MdWpz6EnPICPpra6yT1sLBaJ/eI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-{0}-mmp1yy7un5-mmp1yy7un5.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1s7y80tos", "Integrity": "uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 137, "LastWriteTime": "2025-08-13T05:32:32.7749751+00:00"}, "msJMIXugfRbEtIfP3XJuzEXQMGm0LLvyZwhSUk2babs=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-{0}-agadz9y5vm-agadz9y5vm.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xipduspvx2", "Integrity": "hx+wOH6cBFaJadt5gR8S27Tw+iBDvBvTypuVUsRV3uY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 521, "LastWriteTime": "2025-08-13T05:32:32.775975+00:00"}, "kkXLYrWtJwXk4VuJ/u3zHo5P/ttTgHmkSPvBsPWAPXA=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-{0}-9fmja7pljs-9fmja7pljs.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ch14addp29", "Integrity": "gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 1012, "LastWriteTime": "2025-08-13T05:32:32.7749751+00:00"}, "g2uFpjkberc9XtfxrezR+BILxnwuAUpfLcaN15E7rDg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-{0}-rgycuwl3sw-rgycuwl3sw.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "88o5cg3t4w", "Integrity": "GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 1755, "LastWriteTime": "2025-08-13T05:32:32.7749751+00:00"}, "9fVI014pkOZ8XGPGEufzBbiDUGx+KCvoIoibjyAjPHc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-{0}-kjm33rwg1a-kjm33rwg1a.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s2s2rdqeh5", "Integrity": "XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 656, "LastWriteTime": "2025-08-13T05:32:32.7769747+00:00"}, "Yh4wN4UiawAgNtfDWF7aNr73oft7A9q2ihJ6yzKpgO8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-{0}-awzanx0pu8-awzanx0pu8.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m5zicgu0uv", "Integrity": "ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 1844, "LastWriteTime": "2025-08-13T05:32:32.7749751+00:00"}, "S1cQfU4/aAsKeR8Td1SHUioSQZH6+TsFSfPfE4jfZ4k=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\uslingtyva-{0}-m0sdc2vg34-m0sdc2vg34.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w98asdg00m", "Integrity": "ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 327, "LastWriteTime": "2025-08-13T05:32:32.7749751+00:00"}, "tRXcSyYryoReqruKFh0NpmOnJ1xY+Uoak5k6GIn/0vY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-{0}-0b0bj86z40-0b0bj86z40.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azl6ax9okv", "Integrity": "Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 284, "LastWriteTime": "2025-08-13T05:32:32.7769747+00:00"}, "X5miHwvHqtaBSwzIC/MXPPO6RG1Y8C54ViBHf1VmU5M=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-{0}-e5lgg05xwp-e5lgg05xwp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k78ptnt0os", "Integrity": "MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 219, "LastWriteTime": "2025-08-13T05:32:32.777975+00:00"}, "jxFF4fG2i1Ugn9xcS6B66Lh0vyp38fD8q6f1XOqf7s0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-{0}-ki10xp5gks-ki10xp5gks.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tkabjbg0r", "Integrity": "2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 469, "LastWriteTime": "2025-08-13T05:32:32.7769747+00:00"}, "KqNEy6++364wkXaJjFDm3TL7mzUDBjr7U57918uDMr4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-{0}-s9hcthfn4x-s9hcthfn4x.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxbb0zqj51", "Integrity": "1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 1349, "LastWriteTime": "2025-08-13T05:32:32.7749751+00:00"}, "/wjMXMg73cCmsy00en6U7WRpdwSow/9c7U+SKb+7lxU=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-{0}-idf8r2y2gj-idf8r2y2gj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7vrwdrxykv", "Integrity": "7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 293, "LastWriteTime": "2025-08-13T05:32:32.777975+00:00"}, "nPoTGjUP0XpNIcySwc2wgVWx89lQMAHIuKIMbObRWrk=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-{0}-btwuipzwbp-btwuipzwbp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ov9jz4ar3", "Integrity": "mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 464, "LastWriteTime": "2025-08-13T05:32:32.775975+00:00"}, "IyM8qOYUS2G8xbU9sh7xe7nLR5GIjesCRcbs1+d3pIw=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-{0}-v95crb0bvb-v95crb0bvb.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ao9gsby09", "Integrity": "1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 513, "LastWriteTime": "2025-08-13T05:32:32.7769747+00:00"}, "ROVQJbvkVdQahvt5veWXRbmzF+e3IttJI4oVvohrsJI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\is0nail857-{0}-b0dyrub9as-b0dyrub9as.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fg0nienog3", "Integrity": "8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 276, "LastWriteTime": "2025-08-13T05:32:32.7769747+00:00"}, "76pvba9LkWS3zOhArhFgN+EeMLJHnax5btbIOlUxaQg=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-{0}-1dlotxxwer-1dlotxxwer.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\css\\reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2cbdkxfmct", "Integrity": "Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\css\\reboot.css", "FileLength": 2181, "LastWriteTime": "2025-08-13T05:32:32.777975+00:00"}, "6rkdcEDiwzteQ9CMf0+M8MN/hwnbvC6d+yYf8PbJl/8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-{0}-f8c5bd5212-f8c5bd5212.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\js\\initializersLoader.webview.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mnaqae6vjn", "Integrity": "juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 513, "LastWriteTime": "2025-08-13T05:32:32.777975+00:00"}, "ENWiWsInMyoDqbpHkG2AP6Q/aoxmfHyjCPDNTNjmB/k=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-{0}-sc1npuf73a-sc1npuf73a.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\js\\loading-theme.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pdb27vni4m", "Integrity": "HdnF8U8i1VsH8WQifycVR15DIQmDl4GnEIGPTUlO0cw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\js\\loading-theme.js", "FileLength": 1212, "LastWriteTime": "2025-08-13T05:32:32.777975+00:00"}, "VmQwnHQJvAsPsfV5YU83Z7A/tliMTelugYpZ3JGaAu4=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-{0}-ne44q7dz93-ne44q7dz93.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n4ya9vttwz", "Integrity": "R2DGkBkHmSUAB5w0JjEH+PoJ80+ZhaQRmg9Eyim71ac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 90336, "LastWriteTime": "2025-08-13T05:32:32.7849739+00:00"}, "ROeG25rxrsLu8cM+/C7C8WwkqfYSSmQlKRsp7NY8XLI=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-{0}-kz8gc8cxma-kz8gc8cxma.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t8zskhp41d", "Integrity": "k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 568, "LastWriteTime": "2025-08-13T05:32:32.7769747+00:00"}, "evRU0EfI8sCTH+fewwPK98dOC496AlKLIcS3Zo+iORQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-{0}-nenzw845cc-nenzw845cc.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fk5dwn1z3f", "Integrity": "bwkTa7wTdKt8jgRSKaXQlpEFYlLpB4q4I6a0hz7R5h4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 278625, "LastWriteTime": "2025-08-13T05:32:32.8119738+00:00"}, "EjSq/EQc9Kn/DyX6HIz0v2dfWyMyNhKLi0jQLVXw9D8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\f32kn295nz-{0}-gcvjxldlff-gcvjxldlff.gz", "SourceId": "Microsoft.AspNetCore.Components.WebAssembly.Authentication", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.AspNetCore.Components.WebAssembly.Authentication", "RelativePath": "AuthenticationService.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly.authentication\\9.0.6\\staticwebassets\\AuthenticationService.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bl7261sy3y", "Integrity": "EcPEzZq/MnP6aPd65UU+huGs143nnN3gdVpQgChQm8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly.authentication\\9.0.6\\staticwebassets\\AuthenticationService.js", "FileLength": 74044, "LastWriteTime": "2025-08-13T05:32:32.7829741+00:00"}, "EqM95Pl3Sq19Htp9FMxKPqxMXDs1P1crKTJgTizt+Wo=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\vwxcewz96r-{0}-uwvokvs37j-uwvokvs37j.gz", "SourceId": "ZeroKnow.EBS.WebApp", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "app#[.{fingerprint=uwvokvs37j}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0qbgtlb1k4", "Integrity": "qL8VjSzmSzrz7eesHXL88sHQqr5raqndUHPjc62xq8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\wwwroot\\app.css", "FileLength": 2331, "LastWriteTime": "2025-08-13T05:32:32.777975+00:00"}, "rt/9bX0si4G70YVBAzGCiufEoNXkKxeP2fs9wWImJG0=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\dyd7fyy5ne-{0}-a8m5cweeeb-a8m5cweeeb.gz", "SourceId": "ZeroKnow.EBS.WebApp", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint=a8m5cweeeb}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7r53oxhn9o", "Integrity": "40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\wwwroot\\favicon.ico", "FileLength": 5357, "LastWriteTime": "2025-08-13T05:32:32.777975+00:00"}, "WoivKkik0SiCAt8mmLx3/idbXEVcI6Db0abqh0PomeM=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\1r9lwx3wh0-{0}-uhfllo7vmv-uhfllo7vmv.gz", "SourceId": "ZeroKnow.EBS.WebApp", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "ZeroKnow.EBS.WebApp#[.{fingerprint=uhfllo7vmv}]?.modules.json.gz", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pmmudzy78n", "Integrity": "EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 93, "LastWriteTime": "2025-08-13T05:32:32.7769747+00:00"}, "BGXxebn/caVTZMvU2NkUwbB3VhOLwtIWINnKpNMcvm8=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\1bfwidqiyr-{0}-b6pgxrrsua-b6pgxrrsua.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.b6pgxrrsua.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.b6pgxrrsua.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m8jkaatmrm", "Integrity": "ZqC6drprzt9UEQPYEeFlwrl4I1p8pMYOKnuXfcVyiLk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.12.1\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.b6pgxrrsua.bundle.scp.css", "FileLength": 14352, "LastWriteTime": "2025-08-13T05:32:32.7799751+00:00"}, "9b/bfTbV/zMjJ0DOBcR9i87cZek2Tcy2en2eQU1AhOQ=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\6n3c3cigv9-{0}-mnek33bc45-mnek33bc45.gz", "SourceId": "ZeroKnow.EBS.WebApp", "SourceType": "Computed", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "ZeroKnow.EBS.WebApp#[.{fingerprint=mnek33bc45}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ZeroKnow.EBS.WebApp.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mzo2px474x", "Integrity": "/jfm4gFZpQGbhj5EHhWSxXQNH38POBw9qHT+7gUzenc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ZeroKnow.EBS.WebApp.styles.css", "FileLength": 109, "LastWriteTime": "2025-08-13T05:32:32.777975+00:00"}, "j03SYL87FXtCmgayxS4JP0eZfi9QH4rS98gG+0HTR2U=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\ngpu5la4a9-{0}-fobh1tcq7r-fobh1tcq7r.gz", "SourceId": "ZeroKnow.EBS.WebApp", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "css/tailwind#[.{fingerprint=fobh1tcq7r}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\wwwroot\\css\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qtujtit4tk", "Integrity": "zrfPvm3c8TZrr7gJ9l3N4/zPqEFCFk4SsCvfUtdraIM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\wwwroot\\css\\tailwind.css", "FileLength": 2382, "LastWriteTime": "2025-08-16T03:57:30.2756896+00:00"}}, "CachedCopyCandidates": {}}