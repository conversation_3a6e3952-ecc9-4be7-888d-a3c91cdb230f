/** @type {import('tailwindcss').Config} */
module.exports = {
  prefix: 'tw-',
  content: [
    "./**/*.{razor,html,cshtml}",
    "./Components/**/*.{razor,html}",
    "./Pages/**/*.{razor,html}",
    "../ZeroKnow.EBS.WebApp.Client/**/*.{razor,html}"
  ],
  theme: {
    extend: {
      colors: {
        // Fluent UI color palette for consistency
        'fluent-blue': {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        'fluent-gray': {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
        'fluent-accent': '#0078d4',
        'fluent-accent-hover': '#106ebe',
        'fluent-success': '#107c10',
        'fluent-warning': '#ff8c00',
        'fluent-error': '#d13438',
      },
      fontFamily: {
        'fluent': ['"Segoe UI Variable"', '"Segoe UI"', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'fluent': '4px',
      },
      boxShadow: {
        'fluent': '0 2px 4px rgba(0, 0, 0, 0.1)',
        'fluent-hover': '0 4px 8px rgba(0, 0, 0, 0.15)',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
