@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Fluent UI inspired components */
@layer components {
  .fluent-card {
    @apply bg-white rounded-fluent shadow-fluent border border-fluent-gray-200 hover:shadow-fluent-hover transition-shadow duration-200;
  }
  
  .fluent-button {
    @apply px-4 py-2 rounded-fluent font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-fluent-accent focus:ring-offset-2;
  }
  
  .fluent-button-primary {
    @apply fluent-button bg-fluent-accent text-white hover:bg-fluent-accent-hover;
  }
  
  .fluent-button-secondary {
    @apply fluent-button bg-fluent-gray-100 text-fluent-gray-700 hover:bg-fluent-gray-200 border border-fluent-gray-300;
  }
  
  .fluent-button-outline {
    @apply fluent-button bg-transparent text-fluent-accent border border-fluent-accent hover:bg-fluent-accent hover:text-white;
  }
  
  .fluent-input {
    @apply w-full px-3 py-2 border border-fluent-gray-300 rounded-fluent focus:outline-none focus:ring-2 focus:ring-fluent-accent focus:border-transparent;
  }
  
  .fluent-select {
    @apply fluent-input appearance-none bg-white;
  }
  
  .fluent-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .fluent-badge-active {
    @apply fluent-badge bg-fluent-success text-white;
  }
  
  .fluent-badge-inactive {
    @apply fluent-badge bg-fluent-gray-400 text-white;
  }
  
  .fluent-badge-leave {
    @apply fluent-badge bg-fluent-warning text-white;
  }
  
  .fluent-badge-terminated {
    @apply fluent-badge bg-fluent-error text-white;
  }
  
  .fluent-tab {
    @apply px-4 py-2 text-sm font-medium text-fluent-gray-600 hover:text-fluent-gray-800 border-b-2 border-transparent hover:border-fluent-gray-300 transition-colors duration-200;
  }
  
  .fluent-tab-active {
    @apply fluent-tab text-fluent-accent border-fluent-accent;
  }
  
  .fluent-avatar {
    @apply rounded-full bg-fluent-gray-200 flex items-center justify-center text-fluent-gray-600;
  }
  
  .fluent-icon {
    @apply w-5 h-5 text-fluent-gray-500;
  }
  
  .fluent-search-container {
    @apply relative;
  }
  
  .fluent-search-icon {
    @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-fluent-gray-400 w-4 h-4;
  }
  
  .fluent-search-input {
    @apply fluent-input pl-10;
  }
}
