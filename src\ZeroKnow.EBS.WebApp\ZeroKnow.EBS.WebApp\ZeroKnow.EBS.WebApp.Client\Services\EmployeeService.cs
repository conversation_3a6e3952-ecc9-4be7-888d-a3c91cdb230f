using ZeroKnow.EBS.WebApp.Client.Models;

namespace ZeroKnow.EBS.WebApp.Client.Services
{
    public interface IEmployeeService
    {
        Task<IEnumerable<Employee>> GetEmployeesAsync();
        Task<Employee?> GetEmployeeByIdAsync(int id);
        Task<IEnumerable<Employee>> SearchEmployeesAsync(string searchTerm);
        Task<IEnumerable<Employee>> GetEmployeesByDepartmentAsync(string department);
    }
    
    public class EmployeeService : IEmployeeService
    {
        private readonly List<Employee> _employees;
        
        public EmployeeService()
        {
            _employees = GenerateSampleEmployees();
        }
        
        public async Task<IEnumerable<Employee>> GetEmployeesAsync()
        {
            await Task.Delay(100); // Simulate async operation
            return _employees;
        }
        
        public async Task<Employee?> GetEmployeeByIdAsync(int id)
        {
            await Task.Delay(50); // Simulate async operation
            return _employees.FirstOrDefault(e => e.Id == id);
        }
        
        public async Task<IEnumerable<Employee>> SearchEmployeesAsync(string searchTerm)
        {
            await Task.Delay(100); // Simulate async operation
            
            if (string.IsNullOrWhiteSpace(searchTerm))
                return _employees;
                
            searchTerm = searchTerm.ToLower();
            return _employees.Where(e => 
                e.FirstName.ToLower().Contains(searchTerm) ||
                e.LastName.ToLower().Contains(searchTerm) ||
                e.Position.ToLower().Contains(searchTerm) ||
                e.Department.ToLower().Contains(searchTerm) ||
                e.Email.ToLower().Contains(searchTerm));
        }
        
        public async Task<IEnumerable<Employee>> GetEmployeesByDepartmentAsync(string department)
        {
            await Task.Delay(100); // Simulate async operation
            
            if (string.IsNullOrWhiteSpace(department))
                return _employees;
                
            return _employees.Where(e => e.Department.Equals(department, StringComparison.OrdinalIgnoreCase));
        }
        
        private List<Employee> GenerateSampleEmployees()
        {
            var departments = new[] { "Engineering", "Marketing", "Sales", "Finance", "Human Resources", "Operations", "Customer Service", "IT", "Legal" };
            var positions = new Dictionary<string, string[]>
            {
                ["Engineering"] = new[] { "Software Engineer", "Senior Software Engineer", "Lead Engineer", "Engineering Manager", "DevOps Engineer", "QA Engineer" },
                ["Marketing"] = new[] { "Marketing Manager", "Content Creator", "Digital Marketing Specialist", "Brand Manager", "Marketing Coordinator" },
                ["Sales"] = new[] { "Sales Representative", "Account Manager", "Sales Manager", "Business Development Manager", "Sales Coordinator" },
                ["Finance"] = new[] { "Financial Analyst", "Accountant", "Finance Manager", "Controller", "CFO" },
                ["Human Resources"] = new[] { "HR Generalist", "HR Manager", "Recruiter", "HR Business Partner", "Compensation Analyst" },
                ["Operations"] = new[] { "Operations Manager", "Operations Coordinator", "Process Analyst", "Operations Director" },
                ["Customer Service"] = new[] { "Customer Service Representative", "Customer Success Manager", "Support Specialist", "Customer Service Manager" },
                ["IT"] = new[] { "IT Support Specialist", "System Administrator", "IT Manager", "Network Engineer", "Security Analyst" },
                ["Legal"] = new[] { "Legal Counsel", "Paralegal", "Compliance Officer", "Legal Assistant" }
            };
            
            var firstNames = new[] { "John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa", "James", "Maria", "William", "Jennifer", "Richard", "Patricia", "Charles", "Linda", "Joseph", "Elizabeth", "Thomas", "Barbara", "Christopher", "Susan", "Daniel", "Jessica", "Matthew", "Karen", "Anthony", "Nancy", "Mark", "Betty" };
            var lastNames = new[] { "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson" };
            
            var employees = new List<Employee>();
            var random = new Random(42); // Fixed seed for consistent data
            
            for (int i = 1; i <= 50; i++)
            {
                var department = departments[random.Next(departments.Length)];
                var position = positions[department][random.Next(positions[department].Length)];
                var firstName = firstNames[random.Next(firstNames.Length)];
                var lastName = lastNames[random.Next(lastNames.Length)];
                
                employees.Add(new Employee
                {
                    Id = i,
                    FirstName = firstName,
                    LastName = lastName,
                    Position = position,
                    Department = department,
                    Email = $"{firstName.ToLower()}.{lastName.ToLower()}@company.com",
                    PhoneNumber = $"({random.Next(200, 999)}) {random.Next(200, 999)}-{random.Next(1000, 9999)}",
                    HireDate = DateTime.Now.AddDays(-random.Next(30, 2000)),
                    Status = (EmploymentStatus)random.Next(0, 4),
                    PhotoUrl = $"https://i.pravatar.cc/150?img={i}",
                    ManagerName = i > 10 ? $"{firstNames[random.Next(firstNames.Length)]} {lastNames[random.Next(lastNames.Length)]}" : null,
                    Salary = random.Next(40000, 150000),
                    DateOfBirth = DateTime.Now.AddYears(-random.Next(22, 65)).AddDays(-random.Next(0, 365)),
                    Address = $"{random.Next(100, 9999)} {new[] { "Main St", "Oak Ave", "Pine Rd", "Elm St", "Cedar Ln" }[random.Next(5)]}, City, State {random.Next(10000, 99999)}",
                    EmergencyContactName = $"{firstNames[random.Next(firstNames.Length)]} {lastNames[random.Next(lastNames.Length)]}",
                    EmergencyContactPhone = $"({random.Next(200, 999)}) {random.Next(200, 999)}-{random.Next(1000, 9999)}",
                    LastReviewScore = Math.Round(random.NextDouble() * 4 + 1, 1), // 1.0 to 5.0
                    LastReviewDate = DateTime.Now.AddDays(-random.Next(30, 365)),
                    Notes = random.Next(0, 3) == 0 ? "Excellent performer with strong leadership skills." : null
                });
            }
            
            return employees;
        }
    }
}
