@page "/employees-tailwind"
@using ZeroKnow.EBS.WebApp.Client.Models
@using ZeroKnow.EBS.WebApp.Client.Services
@inject IEmployeeService EmployeeService
@inject NavigationManager Navigation
@rendermode InteractiveAuto

<PageTitle>Employees (Tailwind)</PageTitle>

<div class="tw-container tw-mx-auto tw-px-4 tw-py-6">
    <!-- Header -->
    <div class="tw-flex tw-justify-between tw-items-center tw-mb-6">
        <div class="tw-flex tw-items-center tw-gap-3">
            <FluentIcon Value="@(new Icons.Regular.Size24.People())" Color="Color.Accent" />
            <h1 class="tw-text-3xl tw-font-bold tw-text-gray-900">Employees (Tailwind)</h1>
        </div>
        <div class="fluent-badge tw-bg-gray-100 tw-text-gray-700">
            @filteredEmployees.Count() employees
        </div>
    </div>

    <!-- Search and Filter Controls -->
    <div class="fluent-card tw-p-6 tw-mb-6">
        <div class="tw-flex tw-flex-col md:tw-flex-row tw-gap-4 tw-items-end">
            <!-- Search -->
            <div class="tw-flex-1">
                <label class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">Search Employees</label>
                <div class="tw-relative">
                    <div class="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Search())" Color="Color.Neutral" />
                    </div>
                    <input type="text" @bind="SearchTerm" @bind:event="oninput"
                           placeholder="Search by name, position, department, or email..."
                           class="fluent-input tw-pl-10" />
                </div>
            </div>
            
            <!-- Department Filter -->
            <div class="tw-w-full md:tw-w-48">
                <label class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">Department</label>
                <select @bind="SelectedDepartment" class="fluent-input">
                    <option value="">All Departments</option>
                    @foreach (var dept in departments)
                    {
                        <option value="@dept">@dept</option>
                    }
                </select>
            </div>

            <!-- Status Filter -->
            <div class="tw-w-full md:tw-w-40">
                <label class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">Status</label>
                <select @bind="SelectedStatus" class="fluent-input">
                    <option value="">All Statuses</option>
                    @foreach (var status in Enum.GetValues<EmploymentStatus>())
                    {
                        <option value="@status.ToString()">@status</option>
                    }
                </select>
            </div>

            <!-- Sort -->
            <div class="tw-w-full md:tw-w-40">
                <label class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">Sort By</label>
                <select @bind="SortBy" class="fluent-input">
                    <option value="name">Name</option>
                    <option value="department">Department</option>
                    <option value="position">Position</option>
                    <option value="hiredate">Hire Date</option>
                </select>
            </div>

            <!-- Clear Filters -->
            <button @onclick="ClearFilters" class="fluent-button-secondary" title="Clear all filters">
                <FluentIcon Value="@(new Icons.Regular.Size16.FilterDismiss())" />
            </button>
        </div>
    </div>

    <!-- Loading State -->
    @if (isLoading)
    {
        <div class="tw-flex tw-justify-center tw-items-center tw-py-12">
            <div class="tw-flex tw-items-center tw-gap-3">
                <div class="tw-animate-spin tw-rounded-full tw-h-8 tw-w-8 tw-border-b-2 tw-border-blue-600"></div>
                <span class="tw-text-gray-600">Loading employees...</span>
            </div>
        </div>
    }
    else
    {
        <!-- Employee Cards Grid -->
        <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-6 tw-mb-8">
            @foreach (var employee in paginatedEmployees)
            {
                <div class="fluent-card tw-p-6 tw-cursor-pointer tw-transition tw-duration-200 tw-hover:shadow-lg">
                    <div class="tw-flex tw-items-center tw-gap-4">
                        <!-- Employee Photo -->
                        <div class="tw-w-16 tw-h-16 tw-rounded-full tw-bg-gray-200 tw-flex tw-items-center tw-justify-center tw-flex-shrink-0 tw-overflow-hidden">
                            @if (!string.IsNullOrEmpty(employee.PhotoUrl))
                            {
                                <img src="@employee.PhotoUrl" alt="@employee.FullName" class="tw-w-full tw-h-full tw-object-cover" />
                            }
                            else
                            {
                                <FluentIcon Value="@(new Icons.Regular.Size32.Person())" Color="Color.Neutral" />
                            }
                        </div>

                        <!-- Employee Info -->
                        <div class="tw-flex-1 tw-min-w-0">
                            <div class="tw-flex tw-justify-between tw-items-start tw-mb-2">
                                <h3 class="tw-text-lg tw-font-semibold tw-text-gray-900 tw-truncate">@employee.FullName</h3>
                                <span class="@GetStatusBadgeClass(employee.Status) tw-ml-2">
                                    @employee.Status
                                </span>
                            </div>
                            
                            <div class="tw-space-y-4">
                                <div class="tw-flex tw-items-center tw-gap-2 tw-text-sm tw-text-gray-600">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Briefcase())" Color="Color.Neutral" />
                                    <span class="tw-font-medium">@employee.Position</span>
                                </div>

                                <div class="tw-flex tw-items-center tw-gap-2 tw-text-sm tw-text-gray-600">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Building())" Color="Color.Neutral" />
                                    <span>@employee.Department</span>
                                </div>

                                <div class="tw-flex tw-items-center tw-gap-2 tw-text-sm tw-text-gray-500">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Mail())" Color="Color.Neutral" />
                                    <span class="tw-truncate">@employee.Email</span>
                                </div>

                                <div class="tw-flex tw-items-center tw-gap-2 tw-text-sm tw-text-gray-500">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Calendar())" Color="Color.Neutral" />
                                    <span>Hired: @employee.HireDate.ToString("MMM dd, yyyy")</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Button -->
                        <button @onclick="() => ViewEmployeeDetails(employee.Id)"
                                class="fluent-button-secondary tw-p-2"
                                title="View Details">
                            <FluentIcon Value="@(new Icons.Regular.Size16.ChevronRight())" />
                        </button>
                    </div>
                </div>
            }
        </div>

        <!-- Pagination -->
        @if (totalPages > 1)
        {
            <div class="tw-flex tw-justify-center tw-items-center tw-gap-4 tw-py-6">
                <button @onclick="() => ChangePage(currentPage - 1)"
                        disabled="@(currentPage == 1)"
                        class="fluent-button-secondary tw-disabled:opacity-50 tw-disabled:cursor-not-allowed">
                    <FluentIcon Value="@(new Icons.Regular.Size16.ChevronLeft())" Style="margin-right: 0.5rem;" />
                    Previous
                </button>

                <div class="tw-flex tw-items-center tw-gap-2">
                    <span class="tw-text-sm tw-text-gray-600">Page</span>
                    <input type="number" @bind="CurrentPageString"
                           min="1" max="@totalPages"
                           class="fluent-input tw-w-20 tw-text-center" />
                    <span class="tw-text-sm tw-text-gray-600">of @totalPages</span>
                </div>

                <button @onclick="() => ChangePage(currentPage + 1)"
                        disabled="@(currentPage == totalPages)"
                        class="fluent-button-secondary tw-disabled:opacity-50 tw-disabled:cursor-not-allowed">
                    Next
                    <FluentIcon Value="@(new Icons.Regular.Size16.ChevronRight())" Style="margin-left: 0.5rem;" />
                </button>
            </div>
        }
    }
</div>

@code {
    private IEnumerable<Employee> employees = new List<Employee>();
    private IEnumerable<Employee> filteredEmployees = new List<Employee>();
    private IEnumerable<Employee> paginatedEmployees = new List<Employee>();

    private string searchTerm = string.Empty;
    private string selectedDepartment = string.Empty;
    private string selectedStatus = string.Empty;
    private string sortBy = "name";

    private int currentPage = 1;
    private string currentPageString = "1";
    private int pageSize = 12;
    private int totalPages = 1;

    private bool isLoading = true;

    private readonly string[] departments = new[]
    {
        "Engineering", "Marketing", "Sales", "Finance", "Human Resources",
        "Operations", "Customer Service", "IT", "Legal"
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadEmployees();
    }

    private async Task LoadEmployees()
    {
        isLoading = true;
        StateHasChanged();

        employees = await EmployeeService.GetEmployeesAsync();
        ApplyFiltersAndPagination();

        isLoading = false;
        StateHasChanged();
    }

    private string SearchTerm
    {
        get => searchTerm;
        set
        {
            searchTerm = value;
            currentPage = 1;
            ApplyFiltersAndPagination();
        }
    }

    private string SelectedDepartment
    {
        get => selectedDepartment;
        set
        {
            selectedDepartment = value;
            currentPage = 1;
            ApplyFiltersAndPagination();
        }
    }

    private string SelectedStatus
    {
        get => selectedStatus;
        set
        {
            selectedStatus = value;
            currentPage = 1;
            ApplyFiltersAndPagination();
        }
    }

    private string SortBy
    {
        get => sortBy;
        set
        {
            sortBy = value;
            ApplyFiltersAndPagination();
        }
    }

    private void ClearFilters()
    {
        SearchTerm = string.Empty;
        SelectedDepartment = string.Empty;
        SelectedStatus = string.Empty;
        SortBy = "name";
        currentPage = 1;
        ApplyFiltersAndPagination();
    }

    private void ApplyFiltersAndPagination()
    {
        var filtered = employees.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var search = searchTerm.ToLower();
            filtered = filtered.Where(e =>
                e.FirstName.ToLower().Contains(search) ||
                e.LastName.ToLower().Contains(search) ||
                e.Position.ToLower().Contains(search) ||
                e.Department.ToLower().Contains(search) ||
                e.Email.ToLower().Contains(search));
        }

        // Apply department filter
        if (!string.IsNullOrWhiteSpace(selectedDepartment))
        {
            filtered = filtered.Where(e => e.Department.Equals(selectedDepartment, StringComparison.OrdinalIgnoreCase));
        }

        // Apply status filter
        if (!string.IsNullOrWhiteSpace(selectedStatus))
        {
            if (Enum.TryParse<EmploymentStatus>(selectedStatus, out var status))
            {
                filtered = filtered.Where(e => e.Status == status);
            }
        }

        // Apply sorting
        filtered = sortBy switch
        {
            "name" => filtered.OrderBy(e => e.LastName).ThenBy(e => e.FirstName),
            "department" => filtered.OrderBy(e => e.Department).ThenBy(e => e.LastName),
            "position" => filtered.OrderBy(e => e.Position).ThenBy(e => e.LastName),
            "hiredate" => filtered.OrderByDescending(e => e.HireDate),
            _ => filtered.OrderBy(e => e.LastName).ThenBy(e => e.FirstName)
        };

        filteredEmployees = filtered;

        // Calculate pagination
        totalPages = (int)Math.Ceiling((double)filteredEmployees.Count() / pageSize);
        if (currentPage > totalPages && totalPages > 0)
        {
            currentPage = totalPages;
        }

        // Apply pagination
        paginatedEmployees = filteredEmployees
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize);

        StateHasChanged();
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            currentPageString = page.ToString();
            ApplyFiltersAndPagination();
        }
    }

    private string CurrentPageString
    {
        get => currentPageString;
        set
        {
            currentPageString = value;
            if (int.TryParse(value, out var page))
            {
                ChangePage(page);
            }
        }
    }

    private void ViewEmployeeDetails(int employeeId)
    {
        Navigation.NavigateTo($"/employees-tailwind/{employeeId}");
    }

    private string GetStatusBadgeClass(EmploymentStatus status)
    {
        return status switch
        {
            EmploymentStatus.Active => "fluent-badge-active",
            EmploymentStatus.OnLeave => "fluent-badge-leave",
            EmploymentStatus.Inactive => "fluent-badge-inactive",
            EmploymentStatus.Terminated => "fluent-badge-terminated",
            _ => "fluent-badge-inactive"
        };
    }
}
