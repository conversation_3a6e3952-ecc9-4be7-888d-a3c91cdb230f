/* Tailwind CSS - Essential utilities for HR System */

/* Reset and base styles */
*, ::before, ::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

/* Fluent UI inspired color palette */
:root {
  --fluent-accent: #0078d4;
  --fluent-accent-hover: #106ebe;
  --fluent-success: #107c10;
  --fluent-warning: #ff8c00;
  --fluent-error: #d13438;
  --fluent-gray-50: #f9fafb;
  --fluent-gray-100: #f3f4f6;
  --fluent-gray-200: #e5e7eb;
  --fluent-gray-300: #d1d5db;
  --fluent-gray-400: #9ca3af;
  --fluent-gray-500: #6b7280;
  --fluent-gray-600: #4b5563;
  --fluent-gray-700: #374151;
  --fluent-gray-800: #1f2937;
  --fluent-gray-900: #111827;
}

/* Layout utilities */
.tw-container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
.tw-flex { display: flex; }
.tw-flex-col { flex-direction: column; }
.tw-flex-wrap { flex-wrap: wrap; }
.tw-items-center { align-items: center; }
.tw-items-start { align-items: flex-start; }
.tw-justify-between { justify-content: space-between; }
.tw-justify-center { justify-content: center; }
.tw-gap-1 { gap: 0.25rem; }
.tw-gap-2 { gap: 0.5rem; }
.tw-gap-3 { gap: 0.75rem; }
.tw-gap-4 { gap: 1rem; }
.tw-gap-6 { gap: 1.5rem; }
.tw-gap-8 { gap: 2rem; }

/* Grid utilities */
.tw-grid { display: grid; }
.tw-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.tw-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* Spacing utilities */
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.ml-2 { margin-left: 0.5rem; }

/* Width and height utilities */
.w-full { width: 100%; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-24 { width: 6rem; }
.w-32 { width: 8rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-24 { height: 6rem; }
.h-32 { height: 8rem; }
.h-auto { height: auto; }
.min-h-screen { min-height: 100vh; }

/* Background colors */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: var(--fluent-gray-50); }
.bg-gray-100 { background-color: var(--fluent-gray-100); }
.bg-gray-200 { background-color: var(--fluent-gray-200); }
.bg-blue-500 { background-color: var(--fluent-accent); }
.bg-blue-600 { background-color: var(--fluent-accent-hover); }
.bg-green-500 { background-color: var(--fluent-success); }
.bg-yellow-500 { background-color: var(--fluent-warning); }
.bg-red-500 { background-color: var(--fluent-error); }

/* Text colors */
.text-white { color: #ffffff; }
.text-gray-500 { color: var(--fluent-gray-500); }
.text-gray-600 { color: var(--fluent-gray-600); }
.text-gray-700 { color: var(--fluent-gray-700); }
.text-gray-800 { color: var(--fluent-gray-800); }
.text-gray-900 { color: var(--fluent-gray-900); }
.text-blue-600 { color: var(--fluent-accent); }

/* Border utilities */
.border { border-width: 1px; }
.border-gray-200 { border-color: var(--fluent-gray-200); }
.border-gray-300 { border-color: var(--fluent-gray-300); }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

/* Shadow utilities */
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Typography */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Position utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

/* Display utilities */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

/* Transition utilities */
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }

/* Hover utilities */
.hover\:bg-gray-100:hover { background-color: var(--fluent-gray-100); }
.hover\:bg-blue-600:hover { background-color: var(--fluent-accent-hover); }
.hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }

/* Focus utilities */
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
.focus\:ring-blue-500:focus { --tw-ring-color: var(--fluent-accent); }

/* Custom Fluent UI components */
.fluent-card {
  background-color: #ffffff;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid var(--fluent-gray-200);
  transition: box-shadow 0.2s ease;
}

.fluent-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.fluent-button {
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.fluent-button:focus {
  outline: 2px solid var(--fluent-accent);
  outline-offset: 2px;
}

.fluent-button-primary {
  background-color: var(--fluent-accent);
  color: #ffffff;
}

.fluent-button-primary:hover {
  background-color: var(--fluent-accent-hover);
}

.fluent-button-secondary {
  background-color: var(--fluent-gray-100);
  color: var(--fluent-gray-700);
  border: 1px solid var(--fluent-gray-300);
}

.fluent-button-secondary:hover {
  background-color: var(--fluent-gray-200);
}

.fluent-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--fluent-gray-300);
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.fluent-input:focus {
  outline: none;
  border-color: var(--fluent-accent);
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
}

.fluent-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.625rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.fluent-badge-active { background-color: var(--fluent-success); color: #ffffff; }
.fluent-badge-inactive { background-color: var(--fluent-gray-400); color: #ffffff; }
.fluent-badge-leave { background-color: var(--fluent-warning); color: #ffffff; }
.fluent-badge-terminated { background-color: var(--fluent-error); color: #ffffff; }

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .sm\:tw-flex-row { flex-direction: row; }
  .sm\:tw-justify-between { justify-content: space-between; }
  .sm\:tw-items-start { align-items: flex-start; }
  .sm\:tw-mb-0 { margin-bottom: 0px; }
}

@media (min-width: 768px) {
  .md\:tw-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:tw-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:tw-flex-row { flex-direction: row; }
  .md\:tw-items-center { align-items: center; }
  .md\:tw-w-1\/2 { width: 50%; }
  .md\:tw-w-1\/3 { width: 33.333333%; }
  .md\:tw-w-40 { width: 10rem; }
  .md\:tw-w-48 { width: 12rem; }
}

@media (min-width: 1024px) {
  .lg\:tw-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:tw-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

.fluent-tab {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--fluent-gray-600);
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.fluent-tab:hover {
  color: var(--fluent-gray-800);
  border-bottom-color: var(--fluent-gray-300);
}

.fluent-tab-active {
  color: var(--fluent-accent);
  border-bottom-color: var(--fluent-accent);
}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:flex-row { flex-direction: row; }
  .md\:w-1\/2 { width: 50%; }
  .md\:w-1\/3 { width: 33.333333%; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}
