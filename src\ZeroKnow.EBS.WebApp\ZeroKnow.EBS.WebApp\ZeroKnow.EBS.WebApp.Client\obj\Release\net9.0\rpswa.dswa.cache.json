{"GlobalPropertiesHash": "od7HWwZ5Hmt8YsYbAyCCOg3caNnHC4wt6p46oZTUets=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["2dxsgr3eaJ2xF2a7QACmd/OhOwH44AzhkMEUjMnYqUc=", "hmMr0a1ZLu+XvdiLgvJAZkJGGTvgur7kF2MZA0eiryY=", "indKYLP8ESFhhPWF0Lkn5QihZQaJakw00xhFckRKu+Q=", "7OcCftk1WhjpKXB5wje2up2UA3rVxKriy2XMXXb8f1Y=", "jf4GCIOfPwYA+GZJT05OcPBivY3E2+1eLaKtRBy5mEQ=", "33Z6J/yw2nLkrE+8GGtfXTdTz4hDJc8RkcUJdtg8xIU=", "PpEfXgcHcU8Fq7pIHL+0r9aKr9m+WLcv4L/ocgIhkEc=", "fm74FCRUcgze6sdQYSoJnxdDpnpnTgfxqG5DWvD5htw=", "iVeAch1Yd5hxHtR08Ye8WK+So81K74JiKseT7Ah3yTE=", "RUOhDBpO4oyvKAL5U0aMw6+PPg689xx0Hf/g0qHZGFI=", "E/IWikDrhn0mfWnUC6UrkYKf/d5mrzJ8F6oUGRxYVBQ="], "CachedAssets": {"2dxsgr3eaJ2xF2a7QACmd/OhOwH44AzhkMEUjMnYqUc=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.Development.json", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings.Development#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tswichycbp", "Integrity": "c/lfngzrIF/BxNxQwHaX/Pop1wh4aMKu8dUEyzjHcew=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.Development.json", "FileLength": 127, "LastWriteTime": "2025-08-13T05:31:11.133757+00:00"}, "hmMr0a1ZLu+XvdiLgvJAZkJGGTvgur7kF2MZA0eiryY=": {"Identity": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\appsettings.json", "SourceId": "ZeroKnow.EBS.WebApp.Client", "SourceType": "Discovered", "ContentRoot": "D:\\Development\\ZeroKnowledge\\src\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp\\ZeroKnow.EBS.WebApp.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "appsettings#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tswichycbp", "Integrity": "c/lfngzrIF/BxNxQwHaX/Pop1wh4aMKu8dUEyzjHcew=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\appsettings.json", "FileLength": 127, "LastWriteTime": "2025-08-13T05:31:11.1347558+00:00"}}, "CachedCopyCandidates": {}}